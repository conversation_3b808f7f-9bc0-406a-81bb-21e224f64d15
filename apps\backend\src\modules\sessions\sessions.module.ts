import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';

import { SessionsController } from './sessions.controller';
import { SessionsService } from './sessions.service';
import { Session } from '../../database/entities/session.entity';
import { Message } from '../../database/entities/message.entity';
import { User } from '../../database/entities/user.entity';
import { Agent } from '../../database/entities/agent.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Session, Message, User, Agent]),
    ConfigModule,
  ],
  controllers: [SessionsController],
  providers: [SessionsService],
  exports: [SessionsService],
})
export class SessionsModule {}
