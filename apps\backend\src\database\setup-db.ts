import 'dotenv/config';
import { DataSource } from 'typeorm';

const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || '5432'),
  username: process.env.DATABASE_USERNAME || 'postgres',
  password: process.env.DATABASE_PASSWORD || '',
  database: process.env.DATABASE_NAME || 'synapseai',
  synchronize: false,
  logging: true,
});

async function setupDatabase() {
  try {
    console.log('🔄 Connecting to database...');
    await dataSource.initialize();
    console.log('✅ Database connected successfully');

    console.log('🔄 Setting up database schema...');
    
    // Enable UUID extension
    await dataSource.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
    
    // Create organizations table
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "organizations" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "slug" character varying NOT NULL,
        "description" text,
        "settings" jsonb DEFAULT '{}',
        "isActive" boolean NOT NULL DEFAULT true,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_organizations_slug" UNIQUE ("slug"),
        CONSTRAINT "PK_organizations" PRIMARY KEY ("id")
      )
    `);

    // Create users table
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "users" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "email" character varying NOT NULL,
        "password" character varying NOT NULL,
        "firstName" character varying NOT NULL,
        "lastName" character varying NOT NULL,
        "role" character varying NOT NULL DEFAULT 'user',
        "status" character varying NOT NULL DEFAULT 'active',
        "lastLoginAt" TIMESTAMP,
        "organizationId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_users_email" UNIQUE ("email"),
        CONSTRAINT "PK_users" PRIMARY KEY ("id")
      )
    `);

    // Create agents table
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "agents" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "description" text,
        "prompt" text,
        "model" character varying NOT NULL DEFAULT 'gpt-3.5-turbo',
        "temperature" numeric(3,2) DEFAULT 0.7,
        "maxTokens" integer DEFAULT 1000,
        "isActive" boolean NOT NULL DEFAULT true,
        "organizationId" uuid NOT NULL,
        "createdBy" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_agents" PRIMARY KEY ("id")
      )
    `);

    // Create sessions table
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "sessions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "agentId" uuid NOT NULL,
        "userId" uuid NOT NULL,
        "status" character varying NOT NULL DEFAULT 'active',
        "metadata" jsonb DEFAULT '{}',
        "startedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "endedAt" TIMESTAMP,
        "organizationId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_sessions" PRIMARY KEY ("id")
      )
    `);

    // Create messages table
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "messages" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "sessionId" uuid NOT NULL,
        "role" character varying NOT NULL,
        "content" text NOT NULL,
        "metadata" jsonb DEFAULT '{}',
        "organizationId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_messages" PRIMARY KEY ("id")
      )
    `);

    // Create tools table
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "tools" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "description" text,
        "type" character varying NOT NULL,
        "config" jsonb DEFAULT '{}',
        "isActive" boolean NOT NULL DEFAULT true,
        "organizationId" uuid NOT NULL,
        "createdBy" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_tools" PRIMARY KEY ("id")
      )
    `);

    // Create providers table
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "providers" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "type" character varying NOT NULL,
        "config" jsonb DEFAULT '{}',
        "isActive" boolean NOT NULL DEFAULT true,
        "organizationId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_providers" PRIMARY KEY ("id")
      )
    `);

    // Add foreign key constraints
    await dataSource.query(`
      ALTER TABLE "users" 
      DROP CONSTRAINT IF EXISTS "FK_users_organizationId";
      ALTER TABLE "users" 
      ADD CONSTRAINT "FK_users_organizationId" 
      FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE
    `);

    await dataSource.query(`
      ALTER TABLE "agents" 
      DROP CONSTRAINT IF EXISTS "FK_agents_organizationId";
      ALTER TABLE "agents" 
      ADD CONSTRAINT "FK_agents_organizationId" 
      FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE
    `);

    await dataSource.query(`
      ALTER TABLE "sessions" 
      DROP CONSTRAINT IF EXISTS "FK_sessions_agentId";
      ALTER TABLE "sessions" 
      ADD CONSTRAINT "FK_sessions_agentId" 
      FOREIGN KEY ("agentId") REFERENCES "agents"("id") ON DELETE CASCADE
    `);

    await dataSource.query(`
      ALTER TABLE "messages" 
      DROP CONSTRAINT IF EXISTS "FK_messages_sessionId";
      ALTER TABLE "messages" 
      ADD CONSTRAINT "FK_messages_sessionId" 
      FOREIGN KEY ("sessionId") REFERENCES "sessions"("id") ON DELETE CASCADE
    `);

    // Create indexes
    await dataSource.query(`CREATE INDEX IF NOT EXISTS "IDX_users_organizationId" ON "users" ("organizationId")`);
    await dataSource.query(`CREATE INDEX IF NOT EXISTS "IDX_agents_organizationId" ON "agents" ("organizationId")`);
    await dataSource.query(`CREATE INDEX IF NOT EXISTS "IDX_sessions_agentId" ON "sessions" ("agentId")`);
    await dataSource.query(`CREATE INDEX IF NOT EXISTS "IDX_messages_sessionId" ON "messages" ("sessionId")`);

    console.log('✅ Database schema created successfully');

    // Insert default organization
    const orgResult = await dataSource.query(`
      INSERT INTO "organizations" ("name", "slug", "description") 
      VALUES ('Default Organization', 'default', 'Default organization for development')
      ON CONFLICT ("slug") DO NOTHING
      RETURNING "id"
    `);

    let orgId;
    if (orgResult.length > 0) {
      orgId = orgResult[0].id;
      console.log('✅ Default organization created');
    } else {
      const existingOrg = await dataSource.query(`SELECT "id" FROM "organizations" WHERE "slug" = 'default'`);
      orgId = existingOrg[0].id;
      console.log('✅ Default organization already exists');
    }

    // Insert default admin user
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('admin123', 12);
    
    await dataSource.query(`
      INSERT INTO "users" ("email", "password", "firstName", "lastName", "role", "organizationId") 
      VALUES ('<EMAIL>', $1, 'Admin', 'User', 'admin', $2)
      ON CONFLICT ("email") DO NOTHING
    `, [hashedPassword, orgId]);

    console.log('✅ Default admin user created (<EMAIL> / admin123)');
    console.log('🎉 Database setup completed successfully!');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await dataSource.destroy();
  }
}

setupDatabase();
