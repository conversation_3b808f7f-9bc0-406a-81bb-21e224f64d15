import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Organization } from './organization.entity';
import { Document } from './document.entity';

export enum KnowledgeBaseStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PROCESSING = 'processing',
  ERROR = 'error',
}

export enum KnowledgeBaseType {
  GENERAL = 'general',
  DOMAIN_SPECIFIC = 'domain_specific',
  PERSONAL = 'personal',
  SHARED = 'shared',
}

@Entity('knowledge_bases')
@Index(['organizationId'])
@Index(['createdById'])
@Index(['status'])
export class KnowledgeBase {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: KnowledgeBaseType,
    default: KnowledgeBaseType.GENERAL,
  })
  type: KnowledgeBaseType;

  @Column({
    type: 'enum',
    enum: KnowledgeBaseStatus,
    default: KnowledgeBaseStatus.ACTIVE,
  })
  status: KnowledgeBaseStatus;

  @Column({ type: 'json', nullable: true })
  configuration: {
    embeddingModel?: string;
    chunkSize?: number;
    chunkOverlap?: number;
    vectorDimensions?: number;
    similarityThreshold?: number;
    maxResults?: number;
    indexType?: 'faiss' | 'pinecone' | 'weaviate' | 'qdrant';
  };

  @Column({ type: 'json', nullable: true })
  metadata: {
    tags?: string[];
    category?: string;
    language?: string;
    domain?: string;
    version?: string;
    customFields?: Record<string, any>;
  };

  @Column({ type: 'integer', default: 0 })
  documentCount: number;

  @Column({ type: 'integer', default: 0 })
  vectorCount: number;

  @Column({ type: 'bigint', default: 0 })
  totalSize: number; // in bytes

  @Column({ type: 'timestamp', nullable: true })
  lastIndexedAt: Date;

  @Column({ type: 'json', nullable: true })
  analytics: {
    totalQueries?: number;
    averageRelevanceScore?: number;
    mostQueriedTopics?: string[];
    lastQueried?: Date;
    queryLatency?: number;
  };

  @Column({ type: 'json', nullable: true })
  permissions: {
    public?: boolean;
    allowedUsers?: string[];
    allowedRoles?: string[];
    readOnly?: boolean;
  };

  @Column({ type: 'boolean', default: false })
  isDefault: boolean;

  @Column({ type: 'uuid' })
  organizationId: string;

  @Column({ type: 'uuid' })
  createdById: string;

  @ManyToOne(() => Organization, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'organizationId' })
  organization: Organization;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @OneToMany(() => Document, (document) => document.knowledgeBase)
  documents: Document[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties
  get isActive(): boolean {
    return this.status === KnowledgeBaseStatus.ACTIVE;
  }

  get isProcessing(): boolean {
    return this.status === KnowledgeBaseStatus.PROCESSING;
  }

  get averageDocumentSize(): number {
    return this.documentCount > 0 ? this.totalSize / this.documentCount : 0;
  }

  get indexingProgress(): number {
    // Calculate based on documents vs vectors
    if (this.documentCount === 0) return 100;
    return Math.min(100, (this.vectorCount / this.documentCount) * 100);
  }
}
