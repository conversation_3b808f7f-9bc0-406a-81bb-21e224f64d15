import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { Session, SessionStatus, SessionType } from '../../database/entities/session.entity';
import { Message } from '../../database/entities/message.entity';
import { User } from '../../database/entities/user.entity';
import { Agent } from '../../database/entities/agent.entity';
import { CreateSessionDto } from './dto/create-session.dto';
import { UpdateSessionDto } from './dto/update-session.dto';

@Injectable()
export class SessionsService {
  constructor(
    @InjectRepository(Session)
    private readonly sessionRepository: Repository<Session>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    private readonly configService: ConfigService,
  ) {}

  async create(createSessionDto: CreateSessionDto, currentUser: User): Promise<Session> {
    const { agentId, ...sessionData } = createSessionDto;

    // Verify agent exists and user has access
    const agent = await this.agentRepository.findOne({
      where: { 
        id: agentId, 
        organizationId: currentUser.organizationId 
      },
    });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    // Create session
    const session = this.sessionRepository.create({
      ...sessionData,
      agentId,
      userId: currentUser.id,
      organizationId: currentUser.organizationId,
      status: SessionStatus.ACTIVE,
      lastActivityAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    });

    return this.sessionRepository.save(session);
  }

  async findAll(
    currentUser: User,
    page: number = 1,
    limit: number = 10,
    status?: SessionStatus,
    agentId?: string,
  ) {
    const queryBuilder = this.sessionRepository
      .createQueryBuilder('session')
      .leftJoinAndSelect('session.agent', 'agent')
      .leftJoinAndSelect('session.user', 'user')
      .where('session.organizationId = :organizationId', { 
        organizationId: currentUser.organizationId 
      });

    // Non-admin users can only see their own sessions
    if (!currentUser.isAdmin) {
      queryBuilder.andWhere('session.userId = :userId', { userId: currentUser.id });
    }

    if (status) {
      queryBuilder.andWhere('session.status = :status', { status });
    }

    if (agentId) {
      queryBuilder.andWhere('session.agentId = :agentId', { agentId });
    }

    const [sessions, total] = await queryBuilder
      .orderBy('session.lastActivityAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      sessions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, currentUser: User): Promise<Session> {
    const session = await this.sessionRepository.findOne({
      where: { 
        id, 
        organizationId: currentUser.organizationId 
      },
      relations: ['agent', 'user', 'messages'],
    });

    if (!session) {
      throw new NotFoundException('Session not found');
    }

    // Non-admin users can only access their own sessions
    if (!currentUser.isAdmin && session.userId !== currentUser.id) {
      throw new ForbiddenException('Access denied to this session');
    }

    return session;
  }

  async update(id: string, updateSessionDto: UpdateSessionDto, currentUser: User): Promise<Session> {
    const session = await this.findOne(id, currentUser);

    // Only session owner or admin can update
    if (session.userId !== currentUser.id && !currentUser.isAdmin) {
      throw new ForbiddenException('Cannot update this session');
    }

    Object.assign(session, updateSessionDto);
    session.lastActivityAt = new Date();

    return this.sessionRepository.save(session);
  }

  async remove(id: string, currentUser: User): Promise<void> {
    const session = await this.findOne(id, currentUser);

    // Only session owner or admin can delete
    if (session.userId !== currentUser.id && !currentUser.isAdmin) {
      throw new ForbiddenException('Cannot delete this session');
    }

    await this.sessionRepository.remove(session);
  }

  async updateActivity(sessionId: string): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      lastActivityAt: new Date(),
    });
  }

  async updateStatus(sessionId: string, status: SessionStatus): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      status,
      lastActivityAt: new Date(),
    });
  }

  async getSessionStats(organizationId: string) {
    const totalSessions = await this.sessionRepository.count({
      where: { organizationId },
    });

    const activeSessions = await this.sessionRepository.count({
      where: { organizationId, status: SessionStatus.ACTIVE },
    });

    const completedSessions = await this.sessionRepository.count({
      where: { organizationId, status: SessionStatus.COMPLETED },
    });

    const averageMessages = await this.sessionRepository
      .createQueryBuilder('session')
      .select('AVG(session.messageCount)', 'avg')
      .where('session.organizationId = :organizationId', { organizationId })
      .getRawOne();

    return {
      totalSessions,
      activeSessions,
      completedSessions,
      averageMessages: parseFloat(averageMessages?.avg || '0'),
    };
  }
}
