import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Agent } from '../../database/entities/agent.entity';
import { User } from '../../database/entities/user.entity';

@Injectable()
export class AgentsService {
  constructor(
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
  ) {}

  // Placeholder methods - will be implemented in Agent Builder task
  async findAll(organizationId: string) {
    return this.agentRepository.find({
      where: { organizationId },
      relations: ['createdBy'],
    });
  }

  async findOne(id: string, organizationId: string) {
    return this.agentRepository.findOne({
      where: { id, organizationId },
      relations: ['createdBy', 'sessions'],
    });
  }
}
