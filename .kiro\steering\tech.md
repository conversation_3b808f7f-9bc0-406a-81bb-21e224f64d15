# SynapseAI Technical Stack

## Build System & Package Management

- **Package Manager**: pnpm with workspace support
- **Monorepo Structure**: Apps and packages organized in workspaces
- **Build Tool**: Native TypeScript compilation with Next.js and NestJS build systems
- **Node Version**: >= 18.0.0 required

## Core Technology Stack

### Backend (NestJS)
- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with TypeORM
- **Caching/Sessions**: Redis with ioredis client
- **WebSockets**: Socket.IO with Redis adapter for scaling
- **Authentication**: JWT with Passport.js strategies
- **Validation**: class-validator and class-transformer
- **API Documentation**: Swagger/OpenAPI integration

### Frontend (Next.js)
- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS with Shadcn UI components
- **State Management**: Zustand for client state
- **Data Fetching**: TanStack React Query (React Query)
- **Forms**: React Hook Form with Zod validation
- **Theme**: next-themes for dark/light mode support

### Shared Packages
- **SDK**: TypeScript client library for APIX WebSocket protocol
- **Shared**: Common types, utilities, and validation schemas using Zod

## Key Dependencies

### Backend Dependencies
- `@nestjs/core`, `@nestjs/common` - Core NestJS framework
- `@nestjs/typeorm` - Database ORM integration
- `@nestjs/websockets`, `@nestjs/platform-socket.io` - WebSocket support
- `@nestjs/jwt`, `@nestjs/passport` - Authentication
- `@nestjs/throttler` - Rate limiting
- `bcryptjs` - Password hashing
- `ioredis` - Redis client
- `pg` - PostgreSQL driver
- `socket.io` - WebSocket implementation
- `uuid` - Unique identifier generation

### Frontend Dependencies
- `next` - React framework
- `@tanstack/react-query` - Server state management
- `zustand` - Client state management
- `react-hook-form` - Form handling
- `@hookform/resolvers` - Form validation resolvers
- `zod` - Schema validation
- `axios` - HTTP client
- `lucide-react` - Icon library
- `tailwindcss` - CSS framework

## Common Commands

### Development
```bash
# Start all services in development mode
pnpm dev

# Start individual services
pnpm dev:backend    # NestJS backend on port 3001
pnpm dev:frontend   # Next.js frontend on port 3000

# Install dependencies
pnpm install
```

### Building
```bash
# Build all packages
pnpm build

# Build individual packages
pnpm build:backend
pnpm build:frontend
```

### Testing
```bash
# Run all tests
pnpm test

# Run tests for specific packages
pnpm test:backend
pnpm test:frontend

# Run tests in watch mode
pnpm --filter @synapseai/backend test:watch
```

### Database Operations
```bash
# Run database migrations
pnpm --filter @synapseai/backend db:migrate

# Revert last migration
pnpm --filter @synapseai/backend db:migrate:revert

# Generate new migration
pnpm --filter @synapseai/backend db:migrate:generate

# Seed database with initial data
pnpm --filter @synapseai/backend db:seed

# Reset database (revert, migrate, seed)
pnpm --filter @synapseai/backend db:reset
```

### Code Quality
```bash
# Lint all packages
pnpm lint

# Fix linting issues
pnpm lint:fix

# Type checking
pnpm typecheck

# Clean all build artifacts and node_modules
pnpm clean
```

## Environment Configuration

### Required Environment Variables
- `DATABASE_URL` - PostgreSQL connection string
- `REDIS_URL` - Redis connection string
- `JWT_SECRET` - Secret key for JWT token signing
- `NODE_ENV` - Environment (development/production)

### AI Provider Configuration
- `OPENAI_API_KEY` - OpenAI API key
- `ANTHROPIC_API_KEY` - Claude API key
- `GOOGLE_API_KEY` - Gemini API key
- `GROQ_API_KEY` - Groq API key

## Development Tools

### Code Formatting & Linting
- **ESLint**: Configured for TypeScript with Next.js and NestJS rules
- **Prettier**: Code formatting with Tailwind CSS plugin
- **TypeScript**: Strict mode enabled with path mapping

### Testing Framework
- **Jest**: Unit testing framework
- **Supertest**: API endpoint testing (backend)
- **React Testing Library**: Component testing (frontend)

## Production Deployment

### Process Management
- **PM2**: Process manager for Node.js applications
- **NGINX**: Reverse proxy and static file serving
- **Certbot**: SSL/TLS certificate management

### Recommended Production Setup
```bash
# Build for production
pnpm build

# Start production servers
pnpm start

# Using PM2
pm2 start ecosystem.config.js
```

## Architecture Patterns

### Backend Patterns
- **Module-based architecture**: Feature modules with controllers, services, and entities
- **Dependency injection**: NestJS IoC container
- **Guard-based authentication**: JWT guards with role-based access control
- **Exception filters**: Global error handling and response formatting
- **Interceptors**: Request/response transformation and logging

### Frontend Patterns
- **App Router**: Next.js 14 file-based routing
- **Server/Client Components**: Optimal rendering strategy
- **Custom hooks**: Reusable logic encapsulation
- **Component composition**: Shadcn UI component patterns
- **State management**: Zustand stores with persistence