import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { KnowledgeBase } from './knowledge-base.entity';
import { User } from './user.entity';

export enum DocumentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  INDEXED = 'indexed',
  FAILED = 'failed',
  ARCHIVED = 'archived',
}

export enum DocumentType {
  TEXT = 'text',
  PDF = 'pdf',
  WORD = 'word',
  MARKDOWN = 'markdown',
  HTML = 'html',
  CSV = 'csv',
  JSON = 'json',
  URL = 'url',
  API = 'api',
}

@Entity('documents')
@Index(['knowledgeBaseId'])
@Index(['uploadedById'])
@Index(['status'])
@Index(['type'])
export class Document {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: DocumentType,
  })
  type: DocumentType;

  @Column({
    type: 'enum',
    enum: DocumentStatus,
    default: DocumentStatus.PENDING,
  })
  status: DocumentStatus;

  @Column({ type: 'text', nullable: true })
  content: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  sourceUrl: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  fileName: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  mimeType: string;

  @Column({ type: 'bigint', nullable: true })
  fileSize: number;

  @Column({ type: 'varchar', length: 64, nullable: true })
  checksum: string;

  @Column({ type: 'json', nullable: true })
  metadata: {
    author?: string;
    language?: string;
    tags?: string[];
    category?: string;
    extractedAt?: Date;
    pageCount?: number;
    wordCount?: number;
    customFields?: Record<string, any>;
  };

  @Column({ type: 'json', nullable: true })
  processingInfo: {
    chunks?: number;
    vectors?: number;
    processingTime?: number;
    error?: string;
    retryCount?: number;
    lastProcessedAt?: Date;
  };

  @Column({ type: 'json', nullable: true })
  extractedData: {
    text?: string;
    images?: string[];
    tables?: any[];
    links?: string[];
    structure?: any;
  };

  @Column({ type: 'timestamp', nullable: true })
  indexedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastAccessedAt: Date;

  @Column({ type: 'integer', default: 0 })
  accessCount: number;

  @Column({ type: 'json', nullable: true })
  analytics: {
    queryCount?: number;
    relevanceScore?: number;
    popularChunks?: string[];
    lastQueried?: Date;
  };

  @Column({ type: 'uuid' })
  knowledgeBaseId: string;

  @Column({ type: 'uuid' })
  uploadedById: string;

  @ManyToOne(() => KnowledgeBase, (kb) => kb.documents, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'knowledgeBaseId' })
  knowledgeBase: KnowledgeBase;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'uploadedById' })
  uploadedBy: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties
  get isIndexed(): boolean {
    return this.status === DocumentStatus.INDEXED;
  }

  get isProcessing(): boolean {
    return this.status === DocumentStatus.PROCESSING;
  }

  get hasFailed(): boolean {
    return this.status === DocumentStatus.FAILED;
  }

  get fileSizeFormatted(): string {
    if (!this.fileSize) return '0 B';
    
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(this.fileSize) / Math.log(1024));
    return `${(this.fileSize / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  }

  get processingProgress(): number {
    if (this.status === DocumentStatus.INDEXED) return 100;
    if (this.status === DocumentStatus.PROCESSING) return 50;
    if (this.status === DocumentStatus.FAILED) return 0;
    return 0;
  }
}
