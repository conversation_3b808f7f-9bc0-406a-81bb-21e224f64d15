import { Controller, Get, UseGuards } from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON><PERSON>, ApiBearerAuth } from '@nestjs/swagger';

import { ToolsService } from './tools.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../../database/entities/user.entity';

@ApiTags('tools')
@Controller('tools')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ToolsController {
  constructor(private readonly toolsService: ToolsService) {}

  @Get()
  async findAll(@CurrentUser() currentUser: User) {
    return this.toolsService.findAll(currentUser.organizationId);
  }
}
