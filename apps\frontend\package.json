{"name": "@synapseai/frontend", "version": "1.0.0", "description": "SynapseAI Frontend - Next.js 14 with App Router", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.8.0", "autoprefixer": "^10.4.16", "axios": "^1.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "next": "14.0.0", "next-themes": "^0.2.1", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "zod": "^3.25.76", "zustand": "^4.4.6"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query-devtools": "^5.83.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-beautiful-dnd": "^13.1.6", "@types/react-dom": "^18.2.0", "@types/react-syntax-highlighter": "^15.5.9", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.51.0", "eslint-config-next": "14.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.6", "typescript": "^5.2.0"}}