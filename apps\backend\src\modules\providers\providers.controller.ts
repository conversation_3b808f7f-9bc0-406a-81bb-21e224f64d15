import { Controller, Get, UseGuards } from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON><PERSON>, ApiBearerAuth } from '@nestjs/swagger';

import { ProvidersService } from './providers.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../../database/entities/user.entity';

@ApiTags('providers')
@Controller('providers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ProvidersController {
  constructor(private readonly providersService: ProvidersService) {}

  @Get()
  async findAll(@CurrentUser() currentUser: User) {
    return this.providersService.findAll(currentUser.organizationId);
  }
}
