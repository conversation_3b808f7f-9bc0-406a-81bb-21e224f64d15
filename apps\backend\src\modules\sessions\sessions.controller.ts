import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';

import { SessionsService } from './sessions.service';
import { CreateSessionDto } from './dto/create-session.dto';
import { UpdateSessionDto } from './dto/update-session.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { OrganizationGuard } from '../auth/guards/organization.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { OrganizationResource } from '../auth/decorators/organization-resource.decorator';
import { User } from '../../database/entities/user.entity';
import { SessionStatus } from '../../database/entities/session.entity';

@ApiTags('sessions')
@Controller('sessions')
@UseGuards(JwtAuthGuard, OrganizationGuard)
@ApiBearerAuth()
export class SessionsController {
  constructor(private readonly sessionsService: SessionsService) {}

  @Post()
  @OrganizationResource('sessions')
  @ApiOperation({ summary: 'Create a new session' })
  @ApiResponse({
    status: 201,
    description: 'Session successfully created',
    type: 'object',
  })
  async create(
    @Body() createSessionDto: CreateSessionDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.sessionsService.create(createSessionDto, currentUser);
  }

  @Get()
  @OrganizationResource('sessions')
  @ApiOperation({ summary: 'Get all sessions' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, enum: SessionStatus })
  @ApiQuery({ name: 'agentId', required: false, type: String })
  async findAll(
    @CurrentUser() currentUser: User,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 10,
    @Query('status') status?: SessionStatus,
    @Query('agentId') agentId?: string,
  ) {
    return this.sessionsService.findAll(currentUser, page, limit, status, agentId);
  }

  @Get('stats')
  @OrganizationResource('sessions')
  @ApiOperation({ summary: 'Get session statistics' })
  async getStats(@CurrentUser() currentUser: User) {
    return this.sessionsService.getSessionStats(currentUser.organizationId);
  }

  @Get(':id')
  @OrganizationResource('sessions')
  @ApiOperation({ summary: 'Get session by ID' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.sessionsService.findOne(id, currentUser);
  }

  @Patch(':id')
  @OrganizationResource('sessions')
  @ApiOperation({ summary: 'Update session' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateSessionDto: UpdateSessionDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.sessionsService.update(id, updateSessionDto, currentUser);
  }

  @Delete(':id')
  @OrganizationResource('sessions')
  @ApiOperation({ summary: 'Delete session' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    await this.sessionsService.remove(id, currentUser);
    return { message: 'Session deleted successfully' };
  }
}
