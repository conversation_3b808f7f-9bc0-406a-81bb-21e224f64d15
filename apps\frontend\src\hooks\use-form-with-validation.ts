import { useForm, UseFormProps, FieldValues, Path } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useState, useCallback } from 'react';
import { toastUtils } from '@/lib/toast-utils';

export interface UseFormWithValidationOptions<T extends FieldValues> extends UseFormProps<T> {
  schema: z.ZodSchema<T>;
  onSubmit: (data: T) => Promise<void> | void;
  showToastOnError?: boolean;
  showToastOnSuccess?: boolean;
  successMessage?: string;
  loadingMessage?: string;
}

export function useFormWithValidation<T extends FieldValues>({
  schema,
  onSubmit,
  showToastOnError = true,
  showToastOnSuccess = true,
  successMessage = 'Operation completed successfully',
  loadingMessage = 'Processing your request...',
  ...formOptions
}: UseFormWithValidationOptions<T>) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const form = useForm<T>({
    resolver: zodResolver(schema),
    mode: 'onBlur', // Validate on blur for better UX
    reValidateMode: 'onChange', // Re-validate on change after first validation
    ...formOptions,
  });

  const {
    handleSubmit,
    formState: { errors, isValid, isDirty },
    setError,
    clearErrors,
    trigger,
    watch,
  } = form;

  // Enhanced submit handler with loading states and error handling
  const onSubmitWithValidation = useCallback(
    async (data: T) => {
      if (isSubmitting) return;

      setIsSubmitting(true);
      
      // Show loading toast if enabled
      if (loadingMessage) {
        toastUtils.form.loading(loadingMessage);
      }

      try {
        await onSubmit(data);
        
        // Show success toast if enabled
        if (showToastOnSuccess) {
          toastUtils.form.submitSuccess(successMessage);
        }
      } catch (error: any) {
        console.error('Form submission error:', error);
        
        // Handle validation errors from server
        if (error?.response?.data?.errors) {
          const serverErrors = error.response.data.errors;
          Object.keys(serverErrors).forEach((field) => {
            setError(field as Path<T>, {
              type: 'server',
              message: serverErrors[field],
            });
          });
        }
        
        // Show error toast if enabled
        if (showToastOnError) {
          const errorMessage = error?.response?.data?.message || error?.message || 'An error occurred';
          toastUtils.form.submitError(errorMessage);
        }
        
        throw error; // Re-throw for component-level handling if needed
      } finally {
        setIsSubmitting(false);
      }
    },
    [isSubmitting, onSubmit, showToastOnError, showToastOnSuccess, successMessage, loadingMessage, setError]
  );

  // Real-time field validation
  const validateField = useCallback(
    async (fieldName: Path<T>) => {
      try {
        await trigger(fieldName);
        return true;
      } catch {
        return false;
      }
    },
    [trigger]
  );

  // Get field props for easy integration with form components
  const getFieldProps = useCallback(
    (fieldName: Path<T>) => {
      const fieldError = errors[fieldName];
      const fieldValue = watch(fieldName);
      
      return {
        name: fieldName,
        error: fieldError?.message as string | undefined,
        value: fieldValue,
        onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
          form.setValue(fieldName, e.target.value as any);
          // Clear error when user starts typing
          if (fieldError) {
            clearErrors(fieldName);
          }
        },
        onBlur: () => validateField(fieldName),
        'aria-invalid': !!fieldError,
        'aria-describedby': fieldError ? `${fieldName}-error` : undefined,
      };
    },
    [errors, watch, form, clearErrors, validateField]
  );

  // Check if specific field has error
  const hasFieldError = useCallback(
    (fieldName: Path<T>) => !!errors[fieldName],
    [errors]
  );

  // Get specific field error message
  const getFieldError = useCallback(
    (fieldName: Path<T>) => errors[fieldName]?.message as string | undefined,
    [errors]
  );

  // Validate entire form
  const validateForm = useCallback(async () => {
    const result = await trigger();
    return result;
  }, [trigger]);

  // Reset form with optional default values
  const resetForm = useCallback(
    (defaultValues?: Partial<T>) => {
      form.reset(defaultValues);
      clearErrors();
    },
    [form, clearErrors]
  );

  // Set multiple errors at once (useful for server validation)
  const setMultipleErrors = useCallback(
    (errors: Record<string, string>) => {
      Object.entries(errors).forEach(([field, message]) => {
        setError(field as Path<T>, {
          type: 'server',
          message,
        });
      });
    },
    [setError]
  );

  return {
    // Form instance and methods
    ...form,
    
    // Enhanced submit handler
    handleSubmit: handleSubmit(onSubmitWithValidation),
    
    // State
    isSubmitting,
    isValid,
    isDirty,
    errors,
    
    // Utility methods
    validateField,
    validateForm,
    resetForm,
    getFieldProps,
    hasFieldError,
    getFieldError,
    setMultipleErrors,
    
    // Direct access to form methods
    setValue: form.setValue,
    getValues: form.getValues,
    watch,
    trigger,
    clearErrors,
    setError,
  };
}

// Note: Specialized hooks would need to be implemented differently due to async imports
// For now, use the main useFormWithValidation hook directly with imported schemas
