import { io, Socket } from 'socket.io-client';
import { EventEmitter } from '../utils/event-emitter';
import { 
  AIPXEvent,
  UserMessageEvent,
  UserResponseEvent,
  ControlSignalEvent,
  ThinkingStatusEvent,
  TextChunkEvent,
  ToolCallStartEvent,
  ToolCallResultEvent,
  ToolCallErrorEvent,
  RequestUserInputEvent,
  StateUpdateEvent,
  ErrorEvent,
} from '../types/apix-protocol';

export interface AIPXClientConfig {
  url: string;
  token: string;
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
}

export interface ConnectionStatus {
  connected: boolean;
  connecting: boolean;
  error?: string;
  lastConnected?: Date;
  reconnectAttempts: number;
}

export class AIPXClient extends EventEmitter {
  private socket: Socket | null = null;
  private config: AIPXClientConfig;
  private status: ConnectionStatus = {
    connected: false,
    connecting: false,
    reconnectAttempts: 0,
  };

  constructor(config: AIPXClientConfig) {
    super();
    this.config = {
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      ...config,
    };

    if (this.config.autoConnect) {
      this.connect();
    }
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      this.status.connecting = true;
      this.emit('connecting');

      this.socket = io(this.config.url, {
        auth: {
          token: this.config.token,
        },
        transports: ['websocket', 'polling'],
        reconnection: this.config.reconnection,
        reconnectionAttempts: this.config.reconnectionAttempts,
        reconnectionDelay: this.config.reconnectionDelay,
      });

      this.setupEventHandlers();

      this.socket.on('connect', () => {
        this.status.connected = true;
        this.status.connecting = false;
        this.status.lastConnected = new Date();
        this.status.reconnectAttempts = 0;
        this.emit('connected');
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        this.status.connected = false;
        this.status.connecting = false;
        this.status.error = error.message;
        this.status.reconnectAttempts++;
        this.emit('connection_error', error);
        reject(error);
      });
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.status.connected = false;
    this.status.connecting = false;
    this.emit('disconnected');
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('disconnect', (reason) => {
      this.status.connected = false;
      this.emit('disconnected', reason);
    });

    this.socket.on('reconnect', (attemptNumber) => {
      this.status.connected = true;
      this.status.reconnectAttempts = 0;
      this.emit('reconnected', attemptNumber);
    });

    this.socket.on('reconnect_attempt', (attemptNumber) => {
      this.status.reconnectAttempts = attemptNumber;
      this.emit('reconnect_attempt', attemptNumber);
    });

    // APIX Protocol events
    this.socket.on('thinking_status', (data: ThinkingStatusEvent['data']) => {
      this.emit('thinking_status', data);
    });

    this.socket.on('text_chunk', (data: TextChunkEvent['data']) => {
      this.emit('text_chunk', data);
    });

    this.socket.on('tool_call_start', (data: ToolCallStartEvent['data']) => {
      this.emit('tool_call_start', data);
    });

    this.socket.on('tool_call_result', (data: ToolCallResultEvent['data']) => {
      this.emit('tool_call_result', data);
    });

    this.socket.on('tool_call_error', (data: ToolCallErrorEvent['data']) => {
      this.emit('tool_call_error', data);
    });

    this.socket.on('request_user_input', (data: RequestUserInputEvent['data']) => {
      this.emit('request_user_input', data);
    });

    this.socket.on('state_update', (data: StateUpdateEvent['data']) => {
      this.emit('state_update', data);
    });

    this.socket.on('error', (data: ErrorEvent['data']) => {
      this.emit('error', data);
    });

    // Session events
    this.socket.on('session_joined', (data: any) => {
      this.emit('session_joined', data);
    });

    this.socket.on('session_left', (data: any) => {
      this.emit('session_left', data);
    });

    // Ping/Pong for connection health
    this.socket.on('pong', (data: any) => {
      this.emit('pong', data);
    });
  }

  // Message sending methods
  sendMessage(message: string, options?: {
    agentId?: string;
    toolId?: string;
    context?: Record<string, any>;
  }): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to APIX server');
    }

    const event: UserMessageEvent['data'] = {
      message,
      ...options,
    };

    this.socket.emit('user_message', event);
  }

  sendUserResponse(requestId: string, response: string, approved?: boolean): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to APIX server');
    }

    const event: UserResponseEvent['data'] = {
      requestId,
      response,
      approved,
    };

    this.socket.emit('user_response', event);
  }

  sendControlSignal(action: 'stop' | 'pause' | 'resume' | 'restart', reason?: string): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to APIX server');
    }

    const event: ControlSignalEvent['data'] = {
      action,
      reason,
    };

    this.socket.emit('control_signal', event);
  }

  joinSession(sessionId: string): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to APIX server');
    }

    this.socket.emit('join_session', { sessionId });
  }

  leaveSession(sessionId: string): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to APIX server');
    }

    this.socket.emit('leave_session', { sessionId });
  }

  ping(): void {
    if (!this.socket?.connected) {
      throw new Error('Not connected to APIX server');
    }

    this.socket.emit('ping');
  }

  // Getters
  get connectionStatus(): ConnectionStatus {
    return { ...this.status };
  }

  get isConnected(): boolean {
    return this.status.connected;
  }

  get isConnecting(): boolean {
    return this.status.connecting;
  }
}
