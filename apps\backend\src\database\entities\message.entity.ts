import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Session } from './session.entity';
import { User } from './user.entity';

export enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
  SYSTEM = 'system',
  TOOL = 'tool',
}

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  TOOL_CALL = 'tool_call',
  TOOL_RESULT = 'tool_result',
  ERROR = 'error',
}

export enum MessageStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

@Entity('messages')
@Index(['sessionId'])
@Index(['userId'])
@Index(['role'])
@Index(['createdAt'])
export class Message {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: MessageRole,
  })
  role: MessageRole;

  @Column({
    type: 'enum',
    enum: MessageType,
    default: MessageType.TEXT,
  })
  type: MessageType;

  @Column({
    type: 'enum',
    enum: MessageStatus,
    default: MessageStatus.COMPLETED,
  })
  status: MessageStatus;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'json', nullable: true })
  metadata: {
    tokens?: number;
    model?: string;
    provider?: string;
    latency?: number;
    cost?: number;
    toolCalls?: Array<{
      id: string;
      name: string;
      parameters: Record<string, any>;
      result?: any;
      error?: string;
    }>;
    attachments?: Array<{
      type: 'image' | 'file' | 'url';
      url: string;
      name?: string;
      size?: number;
      mimeType?: string;
    }>;
    reasoning?: string;
    confidence?: number;
  };

  @Column({ type: 'json', nullable: true })
  context: Record<string, any>;

  @Column({ type: 'varchar', length: 255, nullable: true })
  parentMessageId: string;

  @Column({ type: 'integer', default: 0 })
  tokenCount: number;

  @Column({ type: 'decimal', precision: 10, scale: 6, default: 0 })
  cost: number;

  @Column({ type: 'integer', nullable: true })
  processingTime: number; // in milliseconds

  @Column({ type: 'json', nullable: true })
  feedback: {
    rating?: number; // 1-5
    helpful?: boolean;
    comment?: string;
    timestamp?: Date;
  };

  @Column({ type: 'uuid' })
  sessionId: string;

  @Column({ type: 'uuid', nullable: true })
  userId: string;

  @ManyToOne(() => Session, (session) => session.messages, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sessionId' })
  session: Session;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties
  get isFromUser(): boolean {
    return this.role === MessageRole.USER;
  }

  get isFromAssistant(): boolean {
    return this.role === MessageRole.ASSISTANT;
  }

  get hasToolCalls(): boolean {
    return this.metadata?.toolCalls && this.metadata.toolCalls.length > 0;
  }

  get hasAttachments(): boolean {
    return this.metadata?.attachments && this.metadata.attachments.length > 0;
  }

  get isCompleted(): boolean {
    return this.status === MessageStatus.COMPLETED;
  }
}
