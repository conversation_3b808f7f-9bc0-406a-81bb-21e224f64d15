'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/hooks/use-toast';
import { Brain, Send, Square, Play, Pause, RotateCcw } from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: Date;
  metadata?: any;
}

export default function DemoPage() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [currentMessage, setCurrentMessage] = useState('');
  const [socket, setSocket] = useState<any>(null);
  const { toast } = useToast();

  // Initialize WebSocket connection
  useEffect(() => {
    connectToAPX();
    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, []);

  const connectToAPX = async () => {
    setIsConnecting(true);
    
    try {
      // For demo purposes, we'll simulate the connection
      // In a real app, this would use the APIX SDK
      setTimeout(() => {
        setIsConnected(true);
        setIsConnecting(false);
        toast({
          title: 'Connected',
          description: 'Successfully connected to APIX protocol',
        });
        
        // Add welcome message
        const welcomeMessage: Message = {
          id: 'welcome',
          type: 'system',
          content: '🎉 Welcome to the SynapseAI APIX Protocol Demo! Try asking about the weather, time, or say hello.',
          timestamp: new Date(),
        };
        setMessages([welcomeMessage]);
      }, 1000);
    } catch (error) {
      setIsConnecting(false);
      toast({
        title: 'Connection Failed',
        description: 'Failed to connect to APIX server',
        variant: 'destructive',
      });
    }
  };

  const simulateAIResponse = async (userMessage: string) => {
    setIsTyping(true);
    
    // Simulate thinking status
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Check if message requires tool usage
    if (userMessage.toLowerCase().includes('weather')) {
      // Simulate tool call
      const toolMessage: Message = {
        id: `tool_${Date.now()}`,
        type: 'tool',
        content: '🔧 Calling weather tool...',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, toolMessage]);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const toolResultMessage: Message = {
        id: `tool_result_${Date.now()}`,
        type: 'tool',
        content: '✅ Weather data retrieved: 22°C, Sunny, 65% humidity',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, toolResultMessage]);
    }
    
    // Simulate streaming response
    const response = generateResponse(userMessage);
    const words = response.split(' ');
    
    for (let i = 0; i < words.length; i++) {
      const chunk = words[i] + ' ';
      setCurrentMessage(prev => prev + chunk);
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    // Complete the message
    const assistantMessage: Message = {
      id: `assistant_${Date.now()}`,
      type: 'assistant',
      content: currentMessage + words[words.length - 1],
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, assistantMessage]);
    setCurrentMessage('');
    setIsTyping(false);
  };

  const generateResponse = (message: string): string => {
    if (message.toLowerCase().includes('weather')) {
      return 'Based on the weather data I retrieved, it\'s currently 22°C and sunny with 65% humidity. Perfect weather for outdoor activities!';
    } else if (message.toLowerCase().includes('time')) {
      return `The current time is ${new Date().toLocaleTimeString()}. I used the time tool to get this information for you.`;
    } else if (message.toLowerCase().includes('hello') || message.toLowerCase().includes('hi')) {
      return 'Hello! I\'m your AI assistant powered by the SynapseAI platform. I can help you with various tasks using tools and real-time communication through the APIX protocol.';
    } else {
      return `I understand you said: "${message}". This is a simulated AI response demonstrating the APIX protocol with streaming text chunks, tool calling capabilities, and real-time communication features.`;
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !isConnected) return;

    // Add user message
    const userMessage: Message = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);

    const messageToSend = inputValue;
    setInputValue('');

    // Simulate AI response
    await simulateAIResponse(messageToSend);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleControlSignal = (action: string) => {
    const controlMessage: Message = {
      id: `control_${Date.now()}`,
      type: 'system',
      content: `🎛️ Control signal sent: ${action}`,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, controlMessage]);
    
    toast({
      title: 'Control Signal',
      description: `Sent ${action} signal to AI agent`,
    });
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Brain className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold">APIX Protocol Demo</h1>
                <p className="text-sm text-muted-foreground">
                  Real-time AI communication with WebSocket
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {isConnecting && <LoadingSpinner size="sm" />}
              <span className={`text-sm ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
                {isConnecting ? 'Connecting...' : isConnected ? '🟢 Connected' : '🔴 Disconnected'}
              </span>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Chat Interface */}
          <div className="lg:col-span-3">
            <div className="bg-card rounded-lg border h-[600px] flex flex-col">
              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.type === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : message.type === 'system'
                          ? 'bg-muted text-muted-foreground'
                          : message.type === 'tool'
                          ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                          : 'bg-muted text-foreground'
                      }`}
                    >
                      <div className="text-sm">{message.content}</div>
                      <div className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))}

                {/* Current typing message */}
                {isTyping && currentMessage && (
                  <div className="flex justify-start">
                    <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-muted text-foreground">
                      <div className="text-sm">{currentMessage}</div>
                      <div className="text-xs opacity-70 mt-1">typing...</div>
                    </div>
                  </div>
                )}

                {/* Typing indicator */}
                {isTyping && !currentMessage && (
                  <div className="flex justify-start">
                    <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-muted text-foreground">
                      <div className="text-sm flex items-center space-x-2">
                        <LoadingSpinner size="sm" />
                        <span>🤔 Thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Input */}
              <div className="p-4 border-t">
                <div className="flex space-x-2">
                  <Input
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Try: 'What's the weather?' or 'Hello'"
                    disabled={!isConnected}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!isConnected || !inputValue.trim()}
                    size="icon"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Control Panel */}
          <div className="space-y-4">
            <div className="bg-card rounded-lg border p-4">
              <h3 className="font-semibold mb-4">Control Signals</h3>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => handleControlSignal('stop')}
                  disabled={!isConnected}
                >
                  <Square className="h-4 w-4 mr-2" />
                  Stop
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => handleControlSignal('pause')}
                  disabled={!isConnected}
                >
                  <Pause className="h-4 w-4 mr-2" />
                  Pause
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => handleControlSignal('resume')}
                  disabled={!isConnected}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Resume
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => handleControlSignal('restart')}
                  disabled={!isConnected}
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Restart
                </Button>
              </div>
            </div>

            <div className="bg-card rounded-lg border p-4">
              <h3 className="font-semibold mb-4">APIX Features</h3>
              <ul className="text-sm space-y-2 text-muted-foreground">
                <li>✅ Real-time WebSocket</li>
                <li>✅ Streaming responses</li>
                <li>✅ Tool calling</li>
                <li>✅ Control signals</li>
                <li>✅ Thinking status</li>
                <li>✅ Error handling</li>
                <li>✅ Session management</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
