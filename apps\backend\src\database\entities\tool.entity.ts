import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Organization } from './organization.entity';

export enum ToolStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
  ARCHIVED = 'archived',
}

export enum ToolType {
  API = 'api',
  WEBHOOK = 'webhook',
  FUNCTION = 'function',
  DATABASE = 'database',
  FILE_SYSTEM = 'file_system',
  EMAIL = 'email',
  CUSTOM = 'custom',
}

@Entity('tools')
@Index(['organizationId'])
@Index(['createdById'])
@Index(['status'])
@Index(['type'])
export class Tool {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ToolType,
    default: ToolType.API,
  })
  type: ToolType;

  @Column({
    type: 'enum',
    enum: ToolStatus,
    default: ToolStatus.DRAFT,
  })
  status: ToolStatus;

  @Column({ type: 'json' })
  inputSchema: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
    additionalProperties?: boolean;
  };

  @Column({ type: 'json' })
  outputSchema: {
    type: 'object';
    properties: Record<string, any>;
    additionalProperties?: boolean;
  };

  @Column({ type: 'json' })
  configuration: {
    endpoint?: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    headers?: Record<string, string>;
    authentication?: {
      type: 'none' | 'api_key' | 'bearer' | 'basic' | 'oauth2';
      config: Record<string, any>;
    };
    timeout?: number;
    retries?: number;
    rateLimit?: {
      requests: number;
      window: number; // in seconds
    };
  };

  @Column({ type: 'text', nullable: true })
  code: string; // For function-type tools

  @Column({ type: 'json', nullable: true })
  testCases: Array<{
    name: string;
    input: Record<string, any>;
    expectedOutput?: Record<string, any>;
    description?: string;
  }>;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'varchar', length: 255, nullable: true })
  icon: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  version: string;

  @Column({ type: 'boolean', default: false })
  isPublic: boolean;

  @Column({ type: 'json', nullable: true })
  analytics: {
    totalCalls?: number;
    successRate?: number;
    averageLatency?: number;
    lastUsed?: Date;
    errorCount?: number;
  };

  @Column({ type: 'json', nullable: true })
  documentation: {
    usage?: string;
    examples?: Array<{
      title: string;
      description: string;
      input: Record<string, any>;
      output: Record<string, any>;
    }>;
    notes?: string;
  };

  @Column({ type: 'uuid' })
  organizationId: string;

  @Column({ type: 'uuid' })
  createdById: string;

  @ManyToOne(() => Organization, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'organizationId' })
  organization: Organization;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties
  get isActive(): boolean {
    return this.status === ToolStatus.ACTIVE;
  }

  get hasTestCases(): boolean {
    return this.testCases && this.testCases.length > 0;
  }

  get successRate(): number {
    return this.analytics?.successRate || 0;
  }
}
