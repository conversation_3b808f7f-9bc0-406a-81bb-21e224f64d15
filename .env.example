# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/synapseai"
DATABASE_HOST="localhost"
DATABASE_PORT="5432"
DATABASE_USERNAME="synapseai"
DATABASE_PASSWORD="your_secure_password"
DATABASE_NAME="synapseai"

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# JWT Configuration
JWT_SECRET="your_super_secure_jwt_secret_key_here"
JWT_EXPIRES_IN="7d"
JWT_REFRESH_SECRET="your_super_secure_refresh_secret_key_here"
JWT_REFRESH_EXPIRES_IN="30d"

# Application Configuration
NODE_ENV="development"
PORT="3001"
FRONTEND_URL="http://localhost:3000"
BACKEND_URL="http://localhost:3001"
API_PREFIX="api/v1"

# WebSocket Configuration
WS_PORT="3002"
WS_PATH="/apix"

# AI Provider API Keys
OPENAI_API_KEY="sk-your_openai_api_key_here"
ANTHROPIC_API_KEY="sk-ant-your_anthropic_api_key_here"
GOOGLE_API_KEY="your_google_gemini_api_key_here"
GROQ_API_KEY="gsk_your_groq_api_key_here"
MISTRAL_API_KEY="your_mistral_api_key_here"

# File Upload Configuration
MAX_FILE_SIZE="10485760" # 10MB in bytes
UPLOAD_DIR="uploads"
ALLOWED_FILE_TYPES="pdf,txt,doc,docx,md,json,csv"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your_app_password"
FROM_EMAIL="<EMAIL>"

# Rate Limiting
RATE_LIMIT_WINDOW_MS="900000" # 15 minutes
RATE_LIMIT_MAX_REQUESTS="100"

# Security
CORS_ORIGIN="http://localhost:3000"
BCRYPT_ROUNDS="12"

# Analytics & Monitoring
ANALYTICS_ENABLED="true"
LOG_LEVEL="info"

# Widget/Embed Configuration
WIDGET_DOMAIN="widgets.synapseai.com"
EMBED_ALLOWED_ORIGINS="*"

# Production Configuration (uncomment for production)
# SSL_CERT_PATH="/path/to/ssl/cert.pem"
# SSL_KEY_PATH="/path/to/ssl/private.key"
# DOMAIN="yourdomain.com"
# API_DOMAIN="api.yourdomain.com"
# APP_DOMAIN="app.yourdomain.com"
