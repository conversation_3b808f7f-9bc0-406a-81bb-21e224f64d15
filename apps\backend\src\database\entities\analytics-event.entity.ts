import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyTo<PERSON>ne,
  Join<PERSON>olumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Organization } from './organization.entity';

export enum EventType {
  // User events
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  USER_REGISTER = 'user_register',
  
  // Agent events
  AGENT_CREATED = 'agent_created',
  AGENT_UPDATED = 'agent_updated',
  AGENT_DELETED = 'agent_deleted',
  AGENT_ACTIVATED = 'agent_activated',
  AGENT_DEACTIVATED = 'agent_deactivated',
  
  // Session events
  SESSION_STARTED = 'session_started',
  SESSION_ENDED = 'session_ended',
  SESSION_PAUSED = 'session_paused',
  SESSION_RESUMED = 'session_resumed',
  
  // Message events
  MESSAGE_SENT = 'message_sent',
  MESSAGE_RECEIVED = 'message_received',
  MESSAGE_FAILED = 'message_failed',
  
  // Tool events
  TOOL_CREATED = 'tool_created',
  TOOL_UPDATED = 'tool_updated',
  TOOL_DELETED = 'tool_deleted',
  TOOL_EXECUTED = 'tool_executed',
  TOOL_FAILED = 'tool_failed',
  
  // Provider events
  PROVIDER_ADDED = 'provider_added',
  PROVIDER_UPDATED = 'provider_updated',
  PROVIDER_REMOVED = 'provider_removed',
  PROVIDER_ERROR = 'provider_error',
  
  // Knowledge base events
  KNOWLEDGE_BASE_CREATED = 'knowledge_base_created',
  KNOWLEDGE_BASE_UPDATED = 'knowledge_base_updated',
  KNOWLEDGE_BASE_DELETED = 'knowledge_base_deleted',
  DOCUMENT_UPLOADED = 'document_uploaded',
  DOCUMENT_INDEXED = 'document_indexed',
  DOCUMENT_QUERIED = 'document_queried',
  
  // System events
  API_REQUEST = 'api_request',
  API_ERROR = 'api_error',
  SYSTEM_ERROR = 'system_error',
  PERFORMANCE_METRIC = 'performance_metric',
}

export enum EventSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

@Entity('analytics_events')
@Index(['organizationId'])
@Index(['userId'])
@Index(['eventType'])
@Index(['createdAt'])
@Index(['severity'])
export class AnalyticsEvent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: EventType,
  })
  eventType: EventType;

  @Column({
    type: 'enum',
    enum: EventSeverity,
    default: EventSeverity.LOW,
  })
  severity: EventSeverity;

  @Column({ type: 'varchar', length: 255, nullable: true })
  description: string;

  @Column({ type: 'json', nullable: true })
  data: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    sessionId?: string;
    agentId?: string;
    toolId?: string;
    providerId?: string;
    duration?: number;
    cost?: number;
    tokens?: number;
    success?: boolean;
    error?: string;
    customFields?: Record<string, any>;
  };

  @Column({ type: 'varchar', length: 255, nullable: true })
  resourceId: string; // ID of the related resource (agent, tool, etc.)

  @Column({ type: 'varchar', length: 100, nullable: true })
  resourceType: string; // Type of the related resource

  @Column({ type: 'uuid', nullable: true })
  userId: string;

  @Column({ type: 'uuid' })
  organizationId: string;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Organization, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'organizationId' })
  organization: Organization;

  @CreateDateColumn()
  createdAt: Date;

  // Virtual properties
  get isCritical(): boolean {
    return this.severity === EventSeverity.CRITICAL;
  }

  get isError(): boolean {
    return this.eventType.includes('error') || this.eventType.includes('failed');
  }

  get isUserAction(): boolean {
    return this.eventType.startsWith('user_');
  }

  get isSystemEvent(): boolean {
    return this.eventType.startsWith('system_') || this.eventType.startsWith('api_');
  }
}
