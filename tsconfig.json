{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@synapseai/sdk": ["./packages/sdk/src"], "@synapseai/shared": ["./packages/shared/src"], "@/*": ["./apps/frontend/src/*"], "@/components/*": ["./apps/frontend/src/components/*"], "@/lib/*": ["./apps/frontend/src/lib/*"], "@/hooks/*": ["./apps/frontend/src/hooks/*"], "@/store/*": ["./apps/frontend/src/store/*"], "@/types/*": ["./apps/frontend/src/types/*"]}, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "forceConsistentCasingInFileNames": true}, "include": ["apps/**/*", "packages/**/*", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist", ".next"]}