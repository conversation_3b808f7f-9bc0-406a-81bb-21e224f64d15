import React, { forwardRef } from 'react';
import { Input } from './input';
import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle } from 'lucide-react';

export interface ValidatedInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  success?: boolean;
  helperText?: string;
  showValidationIcon?: boolean;
}

const ValidatedInput = forwardRef<HTMLInputElement, ValidatedInputProps>(
  ({ 
    className, 
    error, 
    success, 
    helperText, 
    showValidationIcon = true,
    ...props 
  }, ref) => {
    const hasError = !!error;
    const hasSuccess = success && !hasError;

    return (
      <div className="space-y-1">
        <div className="relative">
          <Input
            className={cn(
              className,
              hasError && 'border-red-500 focus:border-red-500 focus:ring-red-500',
              hasSuccess && 'border-green-500 focus:border-green-500 focus:ring-green-500',
              showValidationIcon && (hasError || hasSuccess) && 'pr-10'
            )}
            ref={ref}
            aria-invalid={hasError}
            aria-describedby={
              error ? `${props.id}-error` : 
              helperText ? `${props.id}-helper` : 
              undefined
            }
            {...props}
          />
          
          {showValidationIcon && (hasError || hasSuccess) && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              {hasError && (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )}
              {hasSuccess && (
                <CheckCircle className="h-4 w-4 text-green-500" />
              )}
            </div>
          )}
        </div>
        
        {error && (
          <p 
            id={`${props.id}-error`}
            className="text-sm text-red-600 flex items-center space-x-1"
          >
            <AlertCircle className="h-3 w-3" />
            <span>{error}</span>
          </p>
        )}
        
        {helperText && !error && (
          <p 
            id={`${props.id}-helper`}
            className="text-sm text-muted-foreground"
          >
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

ValidatedInput.displayName = 'ValidatedInput';

export { ValidatedInput };
