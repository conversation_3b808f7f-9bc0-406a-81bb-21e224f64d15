import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { Input } from './input';
import { Label } from './label';
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react';

export interface FormFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  helperText?: string;
  success?: boolean;
  showValidationIcon?: boolean;
  isPassword?: boolean;
  required?: boolean;
}

const FormField = forwardRef<HTMLInputElement, FormFieldProps>(
  ({
    label,
    error,
    helperText,
    success,
    showValidationIcon = true,
    isPassword = false,
    required = false,
    className,
    id,
    ...props
  }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);
    const hasError = !!error;
    const hasSuccess = success && !hasError;
    const fieldId = id || label.toLowerCase().replace(/\s+/g, '-');

    return (
      <div className="space-y-2">
        <Label 
          htmlFor={fieldId} 
          className={cn(
            'text-sm font-medium',
            hasError && 'text-red-600',
            hasSuccess && 'text-green-600'
          )}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        
        <div className="relative">
          <Input
            id={fieldId}
            ref={ref}
            type={isPassword ? (showPassword ? 'text' : 'password') : props.type}
            className={cn(
              'transition-colors duration-200',
              hasError && 'border-red-500 bg-red-50 focus:border-red-500 focus:ring-red-500/20',
              hasSuccess && 'border-green-500 bg-green-50 focus:border-green-500 focus:ring-green-500/20',
              showValidationIcon && (hasError || hasSuccess) && !isPassword && 'pr-10',
              isPassword && 'pr-20',
              className
            )}
            aria-invalid={hasError}
            aria-describedby={
              error ? `${fieldId}-error` : 
              helperText ? `${fieldId}-helper` : 
              undefined
            }
            {...props}
          />
          
          {/* Validation Icon */}
          {showValidationIcon && (hasError || hasSuccess) && !isPassword && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              {hasError && (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )}
              {hasSuccess && (
                <CheckCircle className="h-4 w-4 text-green-500" />
              )}
            </div>
          )}
          
          {/* Password Toggle */}
          {isPassword && (
            <div className="absolute inset-y-0 right-0 flex items-center">
              {showValidationIcon && (hasError || hasSuccess) && (
                <div className="pr-2">
                  {hasError && (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                  {hasSuccess && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                </div>
              )}
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="pr-3 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors"
                tabIndex={-1}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
          )}
        </div>
        
        {/* Error Message */}
        {error && (
          <div 
            id={`${fieldId}-error`}
            className="flex items-start space-x-2 text-sm text-red-600"
            role="alert"
          >
            <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}
        
        {/* Helper Text */}
        {helperText && !error && (
          <p 
            id={`${fieldId}-helper`}
            className="text-sm text-gray-600"
          >
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

FormField.displayName = 'FormField';

export { FormField };
