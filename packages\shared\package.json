{"name": "@synapseai/shared", "version": "1.0.0", "description": "SynapseAI Shared - Common types, utilities, and constants", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.5.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "jest": "^29.5.0", "rollup": "^4.0.0", "ts-jest": "^29.1.0", "typescript": "^5.1.0"}, "keywords": ["shared", "types", "utilities", "synapseai"], "author": "SynapseAI Team", "license": "MIT"}