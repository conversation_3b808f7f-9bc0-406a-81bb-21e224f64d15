{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "ESNext", "moduleResolution": "node", "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}