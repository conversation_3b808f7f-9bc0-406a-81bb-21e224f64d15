# SynapseAI Product Overview

## What is <PERSON>ynapseA<PERSON>?

SynapseAI is a universal AI orchestration platform that enables users to create, configure, and deploy AI agents through a click-to-configure interface. The platform provides real-time AI interactions via a custom WebSocket protocol called APIX.

## Core Value Proposition

- **No-code AI agent creation**: Visual interface for building AI agents without programming
- **Multi-provider support**: Integrates with OpenAI, Claude, Gemini, Groq, Mistral, and OpenRouter
- **Real-time interactions**: WebSocket-based protocol for streaming AI responses
- **Enterprise-ready**: Multi-tenant architecture with RBAC and organization isolation
- **Embeddable widgets**: Deploy AI agents as embeddable components on any website

## Key Features

### Agent Builder
Click-to-configure AI agents with customizable:
- Role and persona settings
- System prompt templates
- Memory and context management
- Provider selection and routing

### Tool Manager
Create and manage stateless tools that agents can use:
- Input/output schema validation
- Test harness for tool validation
- External API integrations
- Retry and fallback handling

### Real-time Protocol (APIX)
WebSocket-based communication supporting:
- Streaming text responses (`text_chunk`)
- Tool execution events (`tool_call_start`, `tool_call_result`, `tool_call_error`)
- Human-in-the-loop requests (`request_user_input`)
- Session state updates (`state_update`)
- Thinking status indicators (`thinking_status`)

### Human-in-the-Loop (HITL)
- Human oversight and intervention capabilities
- Admin override panels with response history
- Configurable HITL rules per agent/tool

### Knowledge Base & RAG
- Document upload and processing
- Context-aware responses using vector search
- Multiple source types (files, URLs, plain text)

### Widget System
- Embeddable AI components for websites
- Customizable themes and branding
- Responsive design for all devices
- Multiple embed formats (script tags, iframes, CMS plugins)

## Target Users

- **Developers**: Building AI-powered applications and integrations
- **Businesses**: Deploying customer service and support automation
- **Content creators**: Embedding AI assistants in websites and applications
- **Enterprises**: Large-scale AI orchestration with governance and compliance needs