import React, { useState, useEffect, useRef } from 'react';
import { useAIPX } from '../hooks/use-apix';
import {
  ThinkingStatusEvent,
  TextChunkEvent,
  ToolCallStartEvent,
  ToolCallResultEvent,
  RequestUserInputEvent,
} from '../types/apix-protocol';

export interface Message {
  id: string;
  type: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: Date;
  metadata?: {
    thinking?: ThinkingStatusEvent['data'];
    toolCall?: ToolCallStartEvent['data'];
    toolResult?: ToolCallResultEvent['data'];
    userInputRequest?: RequestUserInputEvent['data'];
  };
}

export interface AIPXChatProps {
  url: string;
  token: string;
  sessionId?: string;
  agentId?: string;
  className?: string;
  placeholder?: string;
  onMessage?: (message: Message) => void;
  onError?: (error: any) => void;
}

export function AIPXChat({
  url,
  token,
  sessionId,
  agentId,
  className = '',
  placeholder = 'Type your message...',
  onMessage,
  onError,
}: AIPXChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentMessage, setCurrentMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    isConnected,
    isConnecting,
    sendMessage,
    sendUserResponse,
    joinSession,
  } = useAIPX({
    url,
    token,
    onConnected: () => {
      if (sessionId) {
        joinSession(sessionId);
      }
    },
    onThinkingStatus: (data) => {
      setIsTyping(data.status !== 'complete');
      if (data.status === 'complete') {
        setCurrentMessage('');
      }
    },
    onTextChunk: (data) => {
      if (data.isComplete) {
        // Add complete message
        const message: Message = {
          id: data.messageId || `msg_${Date.now()}`,
          type: 'assistant',
          content: currentMessage + data.chunk,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, message]);
        setCurrentMessage('');
        setIsTyping(false);
        onMessage?.(message);
      } else {
        // Accumulate chunks
        setCurrentMessage(prev => prev + data.chunk);
      }
    },
    onToolCallStart: (data) => {
      const message: Message = {
        id: `tool_${data.callId}`,
        type: 'tool',
        content: `🔧 Calling tool: ${data.toolName}`,
        timestamp: new Date(),
        metadata: { toolCall: data },
      };
      setMessages(prev => [...prev, message]);
      onMessage?.(message);
    },
    onToolCallResult: (data) => {
      const message: Message = {
        id: `tool_result_${data.callId}`,
        type: 'tool',
        content: `✅ Tool completed: ${JSON.stringify(data.result)}`,
        timestamp: new Date(),
        metadata: { toolResult: data },
      };
      setMessages(prev => [...prev, message]);
      onMessage?.(message);
    },
    onRequestUserInput: (data) => {
      const message: Message = {
        id: `input_req_${data.requestId}`,
        type: 'system',
        content: `❓ ${data.prompt}`,
        timestamp: new Date(),
        metadata: { userInputRequest: data },
      };
      setMessages(prev => [...prev, message]);
      onMessage?.(message);
    },
    onError: (data) => {
      const message: Message = {
        id: `error_${Date.now()}`,
        type: 'system',
        content: `❌ Error: ${data.error}`,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, message]);
      onError?.(data);
    },
  });

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, currentMessage]);

  const handleSendMessage = () => {
    if (!inputValue.trim() || !isConnected) return;

    // Add user message
    const userMessage: Message = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);
    onMessage?.(userMessage);

    // Send to APIX
    sendMessage(inputValue, { agentId });
    setInputValue('');
    setIsTyping(true);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleUserResponse = (requestId: string, response: string, approved: boolean) => {
    sendUserResponse(requestId, response, approved);
    
    const responseMessage: Message = {
      id: `user_response_${Date.now()}`,
      type: 'user',
      content: `${approved ? '✅' : '❌'} ${response}`,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, responseMessage]);
    onMessage?.(responseMessage);
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Connection Status */}
      <div className="p-2 bg-gray-100 border-b text-sm">
        {isConnecting && <span className="text-yellow-600">🔄 Connecting...</span>}
        {isConnected && <span className="text-green-600">🟢 Connected</span>}
        {!isConnected && !isConnecting && <span className="text-red-600">🔴 Disconnected</span>}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.type === 'user'
                  ? 'bg-blue-500 text-white'
                  : message.type === 'system'
                  ? 'bg-yellow-100 text-yellow-800'
                  : message.type === 'tool'
                  ? 'bg-purple-100 text-purple-800'
                  : 'bg-gray-200 text-gray-800'
              }`}
            >
              <div className="text-sm">{message.content}</div>
              <div className="text-xs opacity-70 mt-1">
                {message.timestamp.toLocaleTimeString()}
              </div>
              
              {/* User Input Request Actions */}
              {message.metadata?.userInputRequest && (
                <div className="mt-2 space-x-2">
                  <button
                    onClick={() => handleUserResponse(
                      message.metadata!.userInputRequest!.requestId,
                      'Approved',
                      true
                    )}
                    className="px-2 py-1 bg-green-500 text-white text-xs rounded"
                  >
                    Approve
                  </button>
                  <button
                    onClick={() => handleUserResponse(
                      message.metadata!.userInputRequest!.requestId,
                      'Declined',
                      false
                    )}
                    className="px-2 py-1 bg-red-500 text-white text-xs rounded"
                  >
                    Decline
                  </button>
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Current typing message */}
        {isTyping && currentMessage && (
          <div className="flex justify-start">
            <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-200 text-gray-800">
              <div className="text-sm">{currentMessage}</div>
              <div className="text-xs opacity-70 mt-1">typing...</div>
            </div>
          </div>
        )}

        {/* Typing indicator */}
        {isTyping && !currentMessage && (
          <div className="flex justify-start">
            <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-200 text-gray-800">
              <div className="text-sm">🤔 Thinking...</div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t">
        <div className="flex space-x-2">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={!isConnected}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          />
          <button
            onClick={handleSendMessage}
            disabled={!isConnected || !inputValue.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );
}
