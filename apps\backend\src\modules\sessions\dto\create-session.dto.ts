import { IsString, IsUUI<PERSON>, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SessionType } from '../../../database/entities/session.entity';

export class CreateSessionDto {
  @ApiProperty({
    description: 'Session title',
    example: 'Customer Support Chat',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'Session type',
    enum: SessionType,
    example: SessionType.CHAT,
    required: false,
  })
  @IsOptional()
  @IsEnum(SessionType)
  type?: SessionType;

  @ApiProperty({
    description: 'Agent ID to use for this session',
    example: 'uuid-string',
  })
  @IsUUID()
  agentId: string;

  @ApiProperty({
    description: 'Initial context for the session',
    example: { customerType: 'premium', language: 'en' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  context?: Record<string, any>;

  @ApiProperty({
    description: 'Session configuration',
    example: { maxMessages: 100, timeout: 60 },
    required: false,
  })
  @IsOptional()
  @IsObject()
  configuration?: Record<string, any>;

  @ApiProperty({
    description: 'Session metadata',
    example: { userAgent: 'Mozilla/5.0...', referrer: 'https://example.com' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
