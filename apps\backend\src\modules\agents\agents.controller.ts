import { Controller, Get, UseGuards } from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON><PERSON>, <PERSON>pi<PERSON><PERSON>erAuth } from '@nestjs/swagger';

import { AgentsService } from './agents.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../../database/entities/user.entity';

@ApiTags('agents')
@Controller('agents')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AgentsController {
  constructor(private readonly agentsService: AgentsService) {}

  @Get()
  async findAll(@CurrentUser() currentUser: User) {
    return this.agentsService.findAll(currentUser.organizationId);
  }
}
