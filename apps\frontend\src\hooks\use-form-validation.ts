import { useState, useCallback } from 'react';
import { useToast } from './use-toast';
import { FormValidator, ValidationRule } from '@/lib/validation';

export interface FormField {
  value: string;
  rules: ValidationRule;
  fieldName?: string;
  realTimeValidation?: boolean;
}

export interface UseFormValidationOptions {
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
  debounceMs?: number;
}

export function useFormValidation(
  initialFields: Record<string, FormField>,
  options: UseFormValidationOptions = {}
) {
  const { toast } = useToast();
  const [fields, setFields] = useState(initialFields);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidating, setIsValidating] = useState(false);

  const {
    showSuccessToast = true,
    showErrorToast = true,
    debounceMs = 300,
  } = options;

  // Validate a single field
  const validateField = useCallback((fieldKey: string, value: string, showToast = false) => {
    const field = fields[fieldKey];
    if (!field) return { isValid: true };

    const result = FormValidator.validateField(value, field.rules, field.fieldName || fieldKey);
    
    setErrors(prev => ({
      ...prev,
      [fieldKey]: result.error || '',
    }));

    if (showToast && showErrorToast && !result.isValid && result.error) {
      toast({
        title: 'Validation Error',
        description: result.error,
        variant: 'destructive',
      });
    }

    return result;
  }, [fields, showErrorToast, toast]);

  // Validate all fields
  const validateForm = useCallback(() => {
    setIsValidating(true);
    
    const fieldData = Object.entries(fields).reduce((acc, [key, field]) => {
      acc[key] = {
        value: field.value,
        rules: field.rules,
        fieldName: field.fieldName,
      };
      return acc;
    }, {} as Record<string, { value: string; rules: ValidationRule; fieldName?: string }>);

    const result = FormValidator.validateForm(fieldData);
    
    setErrors(result.errors);
    setIsValidating(false);

    if (showErrorToast && !result.isValid && result.firstError) {
      toast({
        title: 'Validation Failed',
        description: result.firstError,
        variant: 'destructive',
      });
    }

    if (showSuccessToast && result.isValid) {
      toast({
        title: 'Validation Passed',
        description: 'All fields are valid',
      });
    }

    return result;
  }, [fields, showErrorToast, showSuccessToast, toast]);

  // Update field value
  const updateField = useCallback((fieldKey: string, value: string) => {
    setFields(prev => ({
      ...prev,
      [fieldKey]: {
        ...prev[fieldKey],
        value,
      },
    }));

    // Real-time validation if enabled
    const field = fields[fieldKey];
    if (field?.realTimeValidation && value.length > 0) {
      setTimeout(() => {
        validateField(fieldKey, value, true);
      }, debounceMs);
    }
  }, [fields, validateField, debounceMs]);

  // Get field props for input components
  const getFieldProps = useCallback((fieldKey: string) => {
    const field = fields[fieldKey];
    const error = errors[fieldKey];

    return {
      value: field?.value || '',
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
        updateField(fieldKey, e.target.value);
      },
      error: error || undefined,
      'aria-invalid': !!error,
      'aria-describedby': error ? `${fieldKey}-error` : undefined,
    };
  }, [fields, errors, updateField]);

  // Reset form
  const resetForm = useCallback(() => {
    setFields(initialFields);
    setErrors({});
    setIsValidating(false);
  }, [initialFields]);

  // Get form data
  const getFormData = useCallback(() => {
    return Object.entries(fields).reduce((acc, [key, field]) => {
      acc[key] = field.value;
      return acc;
    }, {} as Record<string, string>);
  }, [fields]);

  // Check if form is valid
  const isFormValid = useCallback(() => {
    return Object.values(errors).every(error => !error) && 
           Object.values(fields).every(field => {
             if (field.rules.required) {
               return field.value.trim().length > 0;
             }
             return true;
           });
  }, [errors, fields]);

  return {
    fields,
    errors,
    isValidating,
    validateField,
    validateForm,
    updateField,
    getFieldProps,
    resetForm,
    getFormData,
    isFormValid: isFormValid(),
  };
}

// Specialized hooks for common forms
export function useLoginValidation() {
  return useFormValidation({
    email: {
      value: '',
      rules: { required: true },
      fieldName: 'Email',
      realTimeValidation: true,
    },
    password: {
      value: '',
      rules: { required: true, minLength: 6 },
      fieldName: 'Password',
      realTimeValidation: true,
    },
  });
}

export function useRegisterValidation() {
  return useFormValidation({
    firstName: {
      value: '',
      rules: { required: true, minLength: 2, maxLength: 50 },
      fieldName: 'First Name',
      realTimeValidation: true,
    },
    lastName: {
      value: '',
      rules: { required: true, minLength: 2, maxLength: 50 },
      fieldName: 'Last Name',
      realTimeValidation: true,
    },
    email: {
      value: '',
      rules: { required: true },
      fieldName: 'Email',
      realTimeValidation: true,
    },
    password: {
      value: '',
      rules: { required: true, minLength: 8 },
      fieldName: 'Password',
      realTimeValidation: true,
    },
    confirmPassword: {
      value: '',
      rules: { required: true },
      fieldName: 'Confirm Password',
      realTimeValidation: true,
    },
    organizationName: {
      value: '',
      rules: { minLength: 2, maxLength: 100 },
      fieldName: 'Organization Name',
      realTimeValidation: false,
    },
  });
}
