'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { FormField } from '@/components/ui/form-field';
import { AuthLayout, FormLoadingOverlay } from '@/components/auth/auth-layout';
import { useAuthStore } from '@/store/auth-store';
import { useFormWithValidation } from '@/hooks/use-form-with-validation';
import { loginSchema, type LoginFormData } from '@/lib/validations/auth';
import { toastUtils, handleApiError } from '@/lib/toast-utils';

export default function LoginPage() {
  const { login } = useAuthStore();
  const router = useRouter();

  // Form submission handler
  const handleLogin = async (data: LoginFormData) => {
    try {
      await login(data.email, data.password);
      toastUtils.auth.loginSuccess();
      router.push('/dashboard');
    } catch (error: any) {
      console.error('Login error:', error);

      // Handle specific error cases
      if (error?.response?.status === 401) {
        toastUtils.auth.loginError('Invalid email or password. Please check your credentials and try again.');
      } else if (error?.response?.status === 429) {
        toastUtils.auth.loginError('Too many login attempts. Please wait a few minutes before trying again.');
      } else {
        handleApiError(error, 'Login');
      }

      throw error; // Re-throw to prevent form success state
    }
  };

  // Initialize form with validation
  const {
    register,
    handleSubmit,
    formState: { errors },
    isSubmitting,
    getFieldProps,
  } = useFormWithValidation({
    schema: loginSchema,
    onSubmit: handleLogin,
    defaultValues: {
      email: '',
      password: '',
    },
  });



  return (
    <AuthLayout
      title="Welcome Back"
      subtitle="Sign in to your account to continue"
      showBackButton
      backButtonText="Back to Home"
      backButtonHref="/"
    >
      <div className="relative">
        <FormLoadingOverlay isVisible={isSubmitting} />

        <form onSubmit={handleSubmit} className="space-y-6">
          <FormField
            {...register('email')}
            label="Email Address"
            type="email"
            placeholder="Enter your email address"
            error={errors.email?.message}
            disabled={isSubmitting}
            required
            autoComplete="email"
          />

          <FormField
            {...register('password')}
            label="Password"
            isPassword
            placeholder="Enter your password"
            error={errors.password?.message}
            disabled={isSubmitting}
            required
            autoComplete="current-password"
          />

          <div className="flex items-center justify-between">
            <Link
              href="/auth/forgot-password"
              className="text-sm text-blue-600 hover:text-blue-500 hover:underline transition-colors"
            >
              Forgot your password?
            </Link>
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isSubmitting}
            size="lg"
          >
            {isSubmitting ? 'Signing in...' : 'Sign In'}
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Don't have an account?{' '}
            <Link
              href="/auth/register"
              className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
            >
              Create one now
            </Link>
          </p>
        </div>

        {/* Demo Credentials */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800 text-center font-medium">
            Demo Account
          </p>
          <p className="text-xs text-blue-600 text-center mt-1">
            Email: <EMAIL> • Password: admin123
          </p>
        </div>
      </div>
    </AuthLayout>
  );
}
