import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { KnowledgeBase } from '../../database/entities/knowledge-base.entity';
import { Document } from '../../database/entities/document.entity';

@Injectable()
export class KnowledgeService {
  constructor(
    @InjectRepository(KnowledgeBase)
    private readonly knowledgeBaseRepository: Repository<KnowledgeBase>,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
  ) {}

  async findAll(organizationId: string) {
    return this.knowledgeBaseRepository.find({
      where: { organizationId },
    });
  }
}
