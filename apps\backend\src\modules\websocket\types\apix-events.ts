// APIX WebSocket Protocol Event Types

export interface BaseEvent {
  type: string;
  timestamp: number;
  sessionId: string;
  userId?: string;
  metadata?: Record<string, any>;
}

// Client to Server Events
export interface UserMessageEvent extends BaseEvent {
  type: 'user_message';
  data: {
    message: string;
    agentId?: string;
    toolId?: string;
    context?: Record<string, any>;
  };
}

export interface UserResponseEvent extends BaseEvent {
  type: 'user_response';
  data: {
    requestId: string;
    response: string;
    approved?: boolean;
  };
}

export interface ControlSignalEvent extends BaseEvent {
  type: 'control_signal';
  data: {
    action: 'stop' | 'pause' | 'resume' | 'restart';
    reason?: string;
  };
}

// Server to Client Events
export interface ThinkingStatusEvent extends BaseEvent {
  type: 'thinking_status';
  data: {
    status: 'thinking' | 'processing' | 'waiting' | 'complete';
    message?: string;
    progress?: number;
  };
}

export interface TextChunkEvent extends BaseEvent {
  type: 'text_chunk';
  data: {
    chunk: string;
    isComplete: boolean;
    messageId?: string;
  };
}

export interface ToolCallStartEvent extends BaseEvent {
  type: 'tool_call_start';
  data: {
    toolId: string;
    toolName: string;
    parameters: Record<string, any>;
    callId: string;
  };
}

export interface ToolCallResultEvent extends BaseEvent {
  type: 'tool_call_result';
  data: {
    callId: string;
    result: any;
    success: boolean;
    executionTime: number;
  };
}

export interface ToolCallErrorEvent extends BaseEvent {
  type: 'tool_call_error';
  data: {
    callId: string;
    error: string;
    code?: string;
    retryable?: boolean;
  };
}

export interface RequestUserInputEvent extends BaseEvent {
  type: 'request_user_input';
  data: {
    requestId: string;
    prompt: string;
    type: 'confirmation' | 'input' | 'choice';
    options?: string[];
    timeout?: number;
  };
}

export interface StateUpdateEvent extends BaseEvent {
  type: 'state_update';
  data: {
    state: Record<string, any>;
    partial?: boolean;
  };
}

export interface ErrorEvent extends BaseEvent {
  type: 'error';
  data: {
    error: string;
    code?: string;
    details?: Record<string, any>;
    recoverable?: boolean;
  };
}

// Union type for all events
export type AIPXEvent = 
  | UserMessageEvent
  | UserResponseEvent
  | ControlSignalEvent
  | ThinkingStatusEvent
  | TextChunkEvent
  | ToolCallStartEvent
  | ToolCallResultEvent
  | ToolCallErrorEvent
  | RequestUserInputEvent
  | StateUpdateEvent
  | ErrorEvent;

// Event type mapping
export type EventTypeMap = {
  'user_message': UserMessageEvent;
  'user_response': UserResponseEvent;
  'control_signal': ControlSignalEvent;
  'thinking_status': ThinkingStatusEvent;
  'text_chunk': TextChunkEvent;
  'tool_call_start': ToolCallStartEvent;
  'tool_call_result': ToolCallResultEvent;
  'tool_call_error': ToolCallErrorEvent;
  'request_user_input': RequestUserInputEvent;
  'state_update': StateUpdateEvent;
  'error': ErrorEvent;
};

// Helper type for event data
export type EventData<T extends keyof EventTypeMap> = EventTypeMap[T]['data'];
