# Real-Time Engine (APIX) Design Document

## Overview

The Real-Time Engine (APIX) is designed as a scalable, event-driven WebSocket communication system that serves as the central nervous system for real-time interactions across the SynapseAI platform. The system follows a hub-and-spoke architecture where the APIX gateway acts as the central hub, managing WebSocket connections and routing events between various platform modules (agents, tools, workflows, knowledge base, HITL).

The design emphasizes horizontal scalability, fault tolerance, and low-latency event delivery while maintaining strict multi-tenant isolation and security. The system is built using Node.js with Socket.IO for WebSocket management, Redis for event persistence and pub/sub, and integrates seamlessly with the existing NestJS microservices architecture.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Clients"
        WEB[Web Browser]
        MOB[Mobile App]
        API[API Clients]
    end
    
    subgraph "Load Balancer"
        LB[Nginx/HAProxy]
    end
    
    subgraph "APIX Gateway Cluster"
        GW1[Gateway Instance 1]
        GW2[Gateway Instance 2]
        GW3[Gateway Instance N]
    end
    
    subgraph "Event Infrastructure"
        REDIS[(Redis Cluster)]
        QUEUE[Event Queue]
    end
    
    subgraph "Platform Modules"
        AGENT[Agent Service]
        TOOL[Tool Service]
        WORKFLOW[Workflow Service]
        HITL[HITL Service]
        KNOWLEDGE[Knowledge Service]
    end
    
    subgraph "Monitoring & Analytics"
        METRICS[Metrics Service]
        LOGS[Logging Service]
    end
    
    WEB --> LB
    MOB --> LB
    API --> LB
    
    LB --> GW1
    LB --> GW2
    LB --> GW3
    
    GW1 --> REDIS
    GW2 --> REDIS
    GW3 --> REDIS
    
    GW1 --> QUEUE
    GW2 --> QUEUE
    GW3 --> QUEUE
    
    AGENT --> QUEUE
    TOOL --> QUEUE
    WORKFLOW --> QUEUE
    HITL --> QUEUE
    KNOWLEDGE --> QUEUE
    
    GW1 --> METRICS
    GW2 --> METRICS
    GW3 --> METRICS
    
    GW1 --> LOGS
    GW2 --> LOGS
    GW3 --> LOGS
```

### Component Architecture

The APIX system consists of several key components:

1. **WebSocket Gateway**: Manages client connections, authentication, and message routing
2. **Event Router**: Handles event publishing, subscription management, and cross-module routing
3. **Connection Manager**: Manages connection lifecycle, health monitoring, and scaling
4. **Event Store**: Persists events for replay and audit purposes
5. **Client Library**: Frontend SDK for seamless integration

## Components and Interfaces

### WebSocket Gateway Service

**Purpose**: Central hub for managing WebSocket connections and initial message routing.

**Key Responsibilities**:
- WebSocket connection establishment and termination
- JWT-based authentication and authorization
- Connection health monitoring and heartbeat management
- Load balancing across multiple gateway instances
- Rate limiting and connection throttling

**Interface**:
```typescript
interface WebSocketGateway {
  // Connection Management
  handleConnection(socket: Socket, authToken: string): Promise<ConnectionContext>
  handleDisconnection(connectionId: string): Promise<void>
  
  // Message Routing
  routeMessage(connectionId: string, message: EventMessage): Promise<void>
  broadcastToTenant(tenantId: string, event: EventMessage): Promise<void>
  
  // Health & Monitoring
  getConnectionStats(): ConnectionStats
  healthCheck(): HealthStatus
}

interface ConnectionContext {
  connectionId: string
  userId: string
  tenantId: string
  permissions: string[]
  connectedAt: Date
  lastActivity: Date
}
```

### Event Router Service

**Purpose**: Manages event publishing, subscription, and intelligent routing between modules.

**Key Responsibilities**:
- Event schema validation and transformation
- Subscription management with filtering capabilities
- Cross-module event routing and delivery
- Event persistence for replay functionality
- Tenant-based event isolation

**Interface**:
```typescript
interface EventRouter {
  // Publishing
  publishEvent(event: EventMessage, context: PublishContext): Promise<void>
  
  // Subscription Management
  subscribe(connectionId: string, subscription: EventSubscription): Promise<void>
  unsubscribe(connectionId: string, subscriptionId: string): Promise<void>
  
  // Event Routing
  routeEvent(event: EventMessage): Promise<void>
  getSubscribers(eventType: string, tenantId: string): Promise<string[]>
}

interface EventMessage {
  id: string
  type: string
  payload: any
  tenantId: string
  userId: string
  timestamp: Date
  metadata: EventMetadata
}

interface EventSubscription {
  eventTypes: string[]
  filters: EventFilter[]
  connectionId: string
  tenantId: string
}
```

### Connection Manager Service

**Purpose**: Handles connection lifecycle, scaling, and health monitoring across the cluster.

**Key Responsibilities**:
- Connection pool management and optimization
- Automatic scaling based on connection load
- Connection health monitoring and cleanup
- Failover handling and connection migration
- Performance metrics collection

**Interface**:
```typescript
interface ConnectionManager {
  // Connection Lifecycle
  registerConnection(connection: ConnectionContext): Promise<void>
  removeConnection(connectionId: string): Promise<void>
  
  // Health Monitoring
  monitorConnectionHealth(): Promise<void>
  cleanupStaleConnections(): Promise<void>
  
  // Scaling & Load Balancing
  getOptimalGatewayInstance(): Promise<string>
  rebalanceConnections(): Promise<void>
  
  // Metrics
  getConnectionMetrics(): Promise<ConnectionMetrics>
}
```

### Event Store Service

**Purpose**: Provides persistent storage for events with replay capabilities and audit trails.

**Key Responsibilities**:
- Event persistence with configurable retention policies
- Event replay for connection recovery
- Audit trail maintenance for compliance
- Event querying and filtering capabilities
- Performance optimization for high-throughput scenarios

**Interface**:
```typescript
interface EventStore {
  // Event Storage
  storeEvent(event: EventMessage): Promise<void>
  getEvents(query: EventQuery): Promise<EventMessage[]>
  
  // Replay Functionality
  getEventsForReplay(connectionId: string, since: Date): Promise<EventMessage[]>
  markEventAsDelivered(eventId: string, connectionId: string): Promise<void>
  
  // Retention Management
  cleanupExpiredEvents(): Promise<void>
  getStorageMetrics(): Promise<StorageMetrics>
}
```

### Client Library (Frontend)

**Purpose**: Provides a seamless integration layer for frontend applications.

**Key Responsibilities**:
- Automatic connection management and reconnection
- Event subscription and unsubscription
- Integration with frontend state management (Zustand)
- Error handling and retry logic
- Offline support and event queuing

**Interface**:
```typescript
interface APIXClient {
  // Connection Management
  connect(authToken: string): Promise<void>
  disconnect(): Promise<void>
  
  // Event Handling
  subscribe(eventType: string, handler: EventHandler): Promise<string>
  unsubscribe(subscriptionId: string): Promise<void>
  
  // Publishing
  publish(event: EventMessage): Promise<void>
  
  // State Integration
  syncWithStore(store: ZustandStore): void
  
  // Status
  getConnectionStatus(): ConnectionStatus
  getMetrics(): ClientMetrics
}
```

## Data Models

### Core Event Schema

```typescript
interface EventMessage {
  // Core Identification
  id: string                    // Unique event identifier
  type: string                  // Event type (e.g., 'agent.execution.started')
  version: string               // Schema version for compatibility
  
  // Content
  payload: any                  // Event-specific data
  
  // Context
  tenantId: string             // Multi-tenant isolation
  userId: string               // User who triggered the event
  sessionId?: string           // Session context if applicable
  
  // Timing
  timestamp: Date              // Event creation time
  expiresAt?: Date            // Event expiration for cleanup
  
  // Metadata
  metadata: {
    source: string             // Originating service/module
    correlationId?: string     // For tracing related events
    priority: 'low' | 'normal' | 'high' | 'critical'
    retryCount?: number        // For failed delivery tracking
    tags?: string[]            // Additional categorization
  }
}
```

### Event Types Schema

```typescript
// Agent Module Events
interface AgentExecutionStartedEvent {
  type: 'agent.execution.started'
  payload: {
    agentId: string
    conversationId: string
    prompt: string
    parameters: Record<string, any>
  }
}

interface AgentExecutionCompletedEvent {
  type: 'agent.execution.completed'
  payload: {
    agentId: string
    conversationId: string
    response: string
    tokensUsed: number
    executionTime: number
  }
}

// Tool Module Events
interface ToolExecutionStartedEvent {
  type: 'tool.execution.started'
  payload: {
    toolId: string
    parameters: Record<string, any>
    triggeredBy: 'agent' | 'workflow' | 'user'
  }
}

// Workflow Module Events
interface WorkflowStepCompletedEvent {
  type: 'workflow.step.completed'
  payload: {
    workflowId: string
    stepId: string
    result: any
    nextSteps: string[]
  }
}

// HITL Module Events
interface HITLApprovalRequestedEvent {
  type: 'hitl.approval.requested'
  payload: {
    requestId: string
    workflowId: string
    context: any
    approvers: string[]
    deadline?: Date
  }
}
```

### Connection and Subscription Models

```typescript
interface ConnectionContext {
  connectionId: string
  userId: string
  tenantId: string
  permissions: string[]
  connectedAt: Date
  lastActivity: Date
  gatewayInstance: string
  subscriptions: Map<string, EventSubscription>
}

interface EventSubscription {
  id: string
  eventTypes: string[]
  filters: EventFilter[]
  connectionId: string
  tenantId: string
  createdAt: Date
}

interface EventFilter {
  field: string
  operator: 'equals' | 'contains' | 'startsWith' | 'in'
  value: any
}
```

## Error Handling

### Connection Error Handling

The system implements comprehensive error handling for connection-related issues:

1. **Authentication Failures**: Invalid JWT tokens result in immediate connection rejection with specific error codes
2. **Connection Limits**: When connection limits are reached, new connections receive a 429 status with retry-after headers
3. **Network Interruptions**: Automatic reconnection with exponential backoff (1s, 2s, 4s, 8s, max 30s)
4. **Heartbeat Failures**: Connections that fail heartbeat checks are gracefully terminated and cleaned up

### Event Processing Error Handling

1. **Schema Validation Errors**: Invalid events are rejected with detailed validation messages
2. **Delivery Failures**: Failed event deliveries are retried up to 3 times with exponential backoff
3. **Subscription Errors**: Invalid subscriptions are rejected with specific error details
4. **Cross-Module Communication Failures**: Circuit breaker pattern prevents cascade failures

### Error Response Format

```typescript
interface ErrorResponse {
  error: {
    code: string
    message: string
    details?: any
    timestamp: Date
    correlationId: string
  }
}

// Common Error Codes
enum ErrorCodes {
  AUTHENTICATION_FAILED = 'AUTH_001',
  CONNECTION_LIMIT_EXCEEDED = 'CONN_001',
  INVALID_EVENT_SCHEMA = 'EVENT_001',
  SUBSCRIPTION_FAILED = 'SUB_001',
  TENANT_ISOLATION_VIOLATION = 'TENANT_001'
}
```

## Testing Strategy

### Unit Testing

- **Connection Management**: Test connection lifecycle, authentication, and cleanup
- **Event Routing**: Verify correct event routing based on subscriptions and filters
- **Schema Validation**: Ensure all event schemas are properly validated
- **Tenant Isolation**: Verify events are properly isolated by tenant

### Integration Testing

- **End-to-End Event Flow**: Test complete event flow from publishing to delivery
- **Cross-Module Communication**: Verify events route correctly between different modules
- **Client Library Integration**: Test frontend client library with real WebSocket connections
- **Failover Scenarios**: Test connection failover and event replay functionality

### Performance Testing

- **Connection Scalability**: Test system behavior under high connection loads (10k+ concurrent connections)
- **Event Throughput**: Measure event processing capacity (target: 10k+ events/second)
- **Latency Testing**: Ensure sub-100ms event delivery under normal load
- **Memory Usage**: Monitor memory consumption under sustained load

### Load Testing Scenarios

```typescript
// Test Scenarios
const loadTestScenarios = {
  connectionStorm: {
    description: 'Rapid connection establishment',
    connections: 5000,
    rampUpTime: '30s'
  },
  
  eventFlood: {
    description: 'High-frequency event publishing',
    eventsPerSecond: 10000,
    duration: '5m'
  },
  
  subscriptionChurn: {
    description: 'Frequent subscription changes',
    subscriptionsPerSecond: 100,
    duration: '10m'
  }
}
```

This design provides a robust, scalable foundation for real-time communication across the SynapseAI platform while maintaining security, performance, and reliability requirements.