'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { FormField } from '@/components/ui/form-field';
import { PasswordStrength } from '@/components/ui/password-strength';
import { AuthLayout, FormLoadingOverlay } from '@/components/auth/auth-layout';
import { useAuthStore } from '@/store/auth-store';
import { useFormWithValidation } from '@/hooks/use-form-with-validation';
import { registerSchema, type RegisterFormData } from '@/lib/validations/auth';
import { toastUtils, handleApiError } from '@/lib/toast-utils';

export default function RegisterPage() {
  const { register: registerUser } = useAuthStore();
  const router = useRouter();

  // Form submission handler
  const handleRegister = async (data: RegisterFormData) => {
    try {
      await registerUser({
        firstName: data.firstName.trim(),
        lastName: data.lastName.trim(),
        email: data.email.trim().toLowerCase(),
        password: data.password,
        organizationName: data.organizationName?.trim() || undefined,
      });

      toastUtils.auth.registerSuccess();
      router.push('/dashboard');
    } catch (error: any) {
      console.error('Registration error:', error);

      // Handle specific error cases
      if (error?.response?.status === 409) {
        toastUtils.auth.registerError('An account with this email address already exists. Please try logging in instead.');
      } else if (error?.response?.status === 422) {
        toastUtils.auth.registerError('Please check your information and try again.');
      } else {
        handleApiError(error, 'Registration');
      }

      throw error; // Re-throw to prevent form success state
    }
  };

  // Initialize form with validation
  const {
    register,
    handleSubmit,
    formState: { errors },
    isSubmitting,
    watch,
  } = useFormWithValidation({
    schema: registerSchema,
    onSubmit: handleRegister,
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: '',
      organizationName: '',
    },
  });

  // Watch password for strength indicator
  const password = watch('password');



  return (
    <AuthLayout
      title="Create Your Account"
      subtitle="Start building intelligent AI agents today"
      showBackButton
      backButtonText="Back to Login"
      backButtonHref="/auth/login"
    >
      <div className="relative">
        <FormLoadingOverlay isVisible={isSubmitting} />

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Name Fields */}
          <div className="grid grid-cols-2 gap-4">
            <FormField
              {...register('firstName')}
              label="First Name"
              placeholder="John"
              error={errors.firstName?.message}
              disabled={isSubmitting}
              required
              autoComplete="given-name"
            />

            <FormField
              {...register('lastName')}
              label="Last Name"
              placeholder="Doe"
              error={errors.lastName?.message}
              disabled={isSubmitting}
              required
              autoComplete="family-name"
            />
          </div>

          {/* Email Field */}
          <FormField
            {...register('email')}
            label="Email Address"
            type="email"
            placeholder="<EMAIL>"
            error={errors.email?.message}
            disabled={isSubmitting}
            required
            autoComplete="email"
          />

          {/* Organization Field */}
          <FormField
            {...register('organizationName')}
            label="Organization Name"
            placeholder="Acme Corp (optional)"
            error={errors.organizationName?.message}
            disabled={isSubmitting}
            helperText="Leave blank to join the default organization"
            autoComplete="organization"
          />

          {/* Password Field */}
          <div className="space-y-3">
            <FormField
              {...register('password')}
              label="Password"
              isPassword
              placeholder="Create a strong password"
              error={errors.password?.message}
              disabled={isSubmitting}
              required
              autoComplete="new-password"
            />

            {/* Password Strength Indicator */}
            {password && password.length > 0 && (
              <PasswordStrength
                password={password}
                showFeedback={true}
                className="mt-2"
              />
            )}
          </div>

          {/* Confirm Password Field */}
          <FormField
            {...register('confirmPassword')}
            label="Confirm Password"
            isPassword
            placeholder="Confirm your password"
            error={errors.confirmPassword?.message}
            disabled={isSubmitting}
            required
            autoComplete="new-password"
          />

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full"
            disabled={isSubmitting}
            size="lg"
          >
            {isSubmitting ? 'Creating Account...' : 'Create Account'}
          </Button>
        </form>

        {/* Footer Links */}
        <div className="mt-6 space-y-4">
          <div className="text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{' '}
              <Link
                href="/auth/login"
                className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
              >
                Sign in instead
              </Link>
            </p>
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-500">
              By creating an account, you agree to our{' '}
              <Link
                href="/terms"
                className="text-blue-600 hover:text-blue-500 underline"
              >
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link
                href="/privacy"
                className="text-blue-600 hover:text-blue-500 underline"
              >
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}
