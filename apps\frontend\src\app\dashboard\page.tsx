'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth-store';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { 
  Brain, 
  Users, 
  Settings, 
  BarChart3, 
  MessageSquare,
  Zap,
  Database,
  Globe
} from 'lucide-react';

export default function DashboardPage() {
  const { user, isAuthenticated, logout } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, router]);

  if (!isAuthenticated || !user) {
    return (
      <div className="flex h-screen items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const quickActions = [
    {
      icon: Brain,
      title: 'Create Agent',
      description: 'Build a new AI agent',
      href: '/dashboard/agents/new',
    },
    {
      icon: Zap,
      title: 'Create Tool',
      description: 'Add a new tool',
      href: '/dashboard/tools/new',
    },
    {
      icon: Database,
      title: 'Knowledge Base',
      description: 'Manage documents',
      href: '/dashboard/knowledge',
    },
    {
      icon: Globe,
      title: 'Providers',
      description: 'Configure AI providers',
      href: '/dashboard/providers',
    },
  ];

  const stats = [
    { label: 'Active Agents', value: '12', icon: Brain },
    { label: 'Total Sessions', value: '1,234', icon: MessageSquare },
    { label: 'API Calls', value: '45.2K', icon: BarChart3 },
    { label: 'Team Members', value: '8', icon: Users },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Brain className="h-8 w-8 text-primary" />
              <span className="text-2xl font-bold">SynapseAI</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">
                Welcome back, {user.firstName}!
              </span>
              <Button variant="outline" onClick={logout}>
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your AI agents, tools, and integrations
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-card p-6 rounded-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">{stat.label}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
                <stat.icon className="h-8 w-8 text-primary" />
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <div
                key={index}
                className="bg-card p-6 rounded-lg border hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => router.push(action.href)}
              >
                <action.icon className="h-8 w-8 text-primary mb-4" />
                <h3 className="font-semibold mb-2">{action.title}</h3>
                <p className="text-sm text-muted-foreground">{action.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
          <div className="bg-card rounded-lg border p-6">
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No recent activity</h3>
              <p className="text-muted-foreground mb-4">
                Start by creating your first AI agent or tool
              </p>
              <Button onClick={() => router.push('/dashboard/agents/new')}>
                Create Agent
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
