import { Controller, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

export interface HealthCheckResponse {
  status: 'ok' | 'error';
  timestamp: string;
  uptime: number;
  database: {
    status: 'connected' | 'disconnected' | 'error';
    message?: string;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  environment: string;
}

@ApiTags('health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ status: 200, description: 'Health check successful' })
  async healthCheck(): Promise<HealthCheckResponse> {
    this.logger.debug('Health check requested');

    const startTime = Date.now();
    
    // Check database connection
    let databaseStatus: 'connected' | 'disconnected' | 'error' = 'disconnected';
    let databaseMessage: string | undefined;

    try {
      if (this.dataSource.isInitialized) {
        // Test database connection with a simple query
        await this.dataSource.query('SELECT 1');
        databaseStatus = 'connected';
        this.logger.debug('Database connection test successful');
      } else {
        databaseStatus = 'disconnected';
        databaseMessage = 'DataSource not initialized';
        this.logger.warn('Database connection not initialized');
      }
    } catch (error) {
      databaseStatus = 'error';
      databaseMessage = error.message;
      this.logger.error(`Database connection test failed: ${error.message}`, error.stack);
    }

    // Get memory usage
    const memoryUsage = process.memoryUsage();
    const totalMemory = memoryUsage.heapTotal;
    const usedMemory = memoryUsage.heapUsed;
    const memoryPercentage = Math.round((usedMemory / totalMemory) * 100);

    const response: HealthCheckResponse = {
      status: databaseStatus === 'connected' ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      database: {
        status: databaseStatus,
        message: databaseMessage,
      },
      memory: {
        used: Math.round(usedMemory / 1024 / 1024), // MB
        total: Math.round(totalMemory / 1024 / 1024), // MB
        percentage: memoryPercentage,
      },
      environment: process.env.NODE_ENV || 'development',
    };

    const duration = Date.now() - startTime;
    this.logger.log(`Health check completed in ${duration}ms - Status: ${response.status}`);

    return response;
  }

  @Get('database')
  @ApiOperation({ summary: 'Database-specific health check' })
  @ApiResponse({ status: 200, description: 'Database health check' })
  async databaseHealth() {
    this.logger.debug('Database health check requested');

    try {
      // Test basic connection
      const connectionTest = await this.dataSource.query('SELECT NOW() as current_time');
      
      // Test if required tables exist
      const tableChecks = await Promise.all([
        this.dataSource.query("SELECT to_regclass('users') as users_table"),
        this.dataSource.query("SELECT to_regclass('organizations') as organizations_table"),
      ]);

      // Get database version
      const versionResult = await this.dataSource.query('SELECT version()');
      
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        connection: 'active',
        currentTime: connectionTest[0].current_time,
        version: versionResult[0].version,
        tables: {
          users: tableChecks[0][0].users_table !== null,
          organizations: tableChecks[1][0].organizations_table !== null,
        },
      };
    } catch (error) {
      this.logger.error(`Database health check failed: ${error.message}`, error.stack);
      
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        connection: 'failed',
        error: error.message,
      };
    }
  }
}
