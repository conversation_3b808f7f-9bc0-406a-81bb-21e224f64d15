import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { Organization } from './organization.entity';

export enum ProviderType {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  GOOGLE = 'google',
  GROQ = 'groq',
  MISTRAL = 'mistral',
  OPENROUTER = 'openrouter',
  CUSTOM = 'custom',
}

export enum ProviderStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
  TESTING = 'testing',
}

@Entity('providers')
@Index(['organizationId'])
@Index(['type'])
@Index(['status'])
export class Provider {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({
    type: 'enum',
    enum: ProviderType,
  })
  type: ProviderType;

  @Column({
    type: 'enum',
    enum: ProviderStatus,
    default: ProviderStatus.INACTIVE,
  })
  status: ProviderStatus;

  @Column({ type: 'json' })
  @Exclude()
  credentials: {
    apiKey?: string;
    apiSecret?: string;
    endpoint?: string;
    organizationId?: string;
    projectId?: string;
    region?: string;
  };

  @Column({ type: 'json', nullable: true })
  configuration: {
    defaultModel?: string;
    availableModels?: string[];
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    timeout?: number;
    retries?: number;
    rateLimit?: {
      requests: number;
      window: number; // in seconds
    };
  };

  @Column({ type: 'json', nullable: true })
  capabilities: {
    textGeneration?: boolean;
    chatCompletion?: boolean;
    streaming?: boolean;
    functionCalling?: boolean;
    imageGeneration?: boolean;
    imageAnalysis?: boolean;
    embeddings?: boolean;
    fineTuning?: boolean;
  };

  @Column({ type: 'json', nullable: true })
  pricing: {
    inputTokens?: number; // cost per 1K tokens
    outputTokens?: number; // cost per 1K tokens
    currency?: string;
    model?: string;
  };

  @Column({ type: 'json', nullable: true })
  limits: {
    requestsPerMinute?: number;
    requestsPerDay?: number;
    tokensPerMinute?: number;
    tokensPerDay?: number;
    concurrentRequests?: number;
  };

  @Column({ type: 'json', nullable: true })
  analytics: {
    totalRequests?: number;
    totalTokens?: number;
    totalCost?: number;
    averageLatency?: number;
    errorRate?: number;
    lastUsed?: Date;
    uptime?: number;
  };

  @Column({ type: 'json', nullable: true })
  healthCheck: {
    lastCheck?: Date;
    status?: 'healthy' | 'degraded' | 'down';
    latency?: number;
    error?: string;
  };

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'boolean', default: true })
  isDefault: boolean;

  @Column({ type: 'integer', default: 0 })
  priority: number; // Higher number = higher priority

  @Column({ type: 'uuid' })
  organizationId: string;

  @ManyToOne(() => Organization, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'organizationId' })
  organization: Organization;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties
  get isActive(): boolean {
    return this.status === ProviderStatus.ACTIVE;
  }

  get isHealthy(): boolean {
    return this.healthCheck?.status === 'healthy';
  }

  get costPerRequest(): number {
    if (!this.analytics?.totalRequests || !this.analytics?.totalCost) {
      return 0;
    }
    return this.analytics.totalCost / this.analytics.totalRequests;
  }

  get tokensPerRequest(): number {
    if (!this.analytics?.totalRequests || !this.analytics?.totalTokens) {
      return 0;
    }
    return this.analytics.totalTokens / this.analytics.totalRequests;
  }
}
