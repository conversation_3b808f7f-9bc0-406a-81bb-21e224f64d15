import {
  WebSocketGateway as WSGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

import { WebSocketService } from './websocket.service';
import { WsJwtGuard } from './guards/ws-jwt.guard';
import { 
  AIPXEvent,
  UserMessageEvent,
  ThinkingStatusEvent,
  TextChunkEvent,
  ToolCallStartEvent,
  ToolCallResultEvent,
  ToolCallErrorEvent,
  RequestUserInputEvent,
  UserResponseEvent,
  StateUpdateEvent,
  ErrorEvent,
  ControlSignalEvent,
} from './types/apix-events';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  organizationId?: string;
  sessionId?: string;
}

@WSGateway({
  path: '/apix',
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: true,
  },
  transports: ['websocket', 'polling'],
})
export class WebSocketGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebSocketGateway.name);

  constructor(
    private readonly webSocketService: WebSocketService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('WebSocket Gateway initialized');
    this.webSocketService.setServer(server);
  }

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token = this.extractTokenFromSocket(client);
      
      if (!token) {
        this.logger.warn(`Client ${client.id} connected without token`);
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token, {
        secret: this.configService.get('JWT_SECRET'),
      });

      client.userId = payload.sub;
      client.organizationId = payload.organizationId;

      await this.webSocketService.handleConnection(client);
      
      this.logger.log(`Client ${client.id} connected (User: ${client.userId})`);
    } catch (error) {
      this.logger.error(`Connection error for client ${client.id}:`, error.message);
      client.disconnect();
    }
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    await this.webSocketService.handleDisconnection(client);
    this.logger.log(`Client ${client.id} disconnected`);
  }

  @SubscribeMessage('user_message')
  @UseGuards(WsJwtGuard)
  async handleUserMessage(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: UserMessageEvent['data'],
  ): Promise<void> {
    await this.webSocketService.handleUserMessage(client, data);
  }

  @SubscribeMessage('user_response')
  @UseGuards(WsJwtGuard)
  async handleUserResponse(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: UserResponseEvent['data'],
  ): Promise<void> {
    await this.webSocketService.handleUserResponse(client, data);
  }

  @SubscribeMessage('control_signal')
  @UseGuards(WsJwtGuard)
  async handleControlSignal(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: ControlSignalEvent['data'],
  ): Promise<void> {
    await this.webSocketService.handleControlSignal(client, data);
  }

  @SubscribeMessage('join_session')
  @UseGuards(WsJwtGuard)
  async handleJoinSession(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { sessionId: string },
  ): Promise<void> {
    await this.webSocketService.joinSession(client, data.sessionId);
  }

  @SubscribeMessage('leave_session')
  @UseGuards(WsJwtGuard)
  async handleLeaveSession(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { sessionId: string },
  ): Promise<void> {
    await this.webSocketService.leaveSession(client, data.sessionId);
  }

  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: Socket): void {
    client.emit('pong', { timestamp: Date.now() });
  }

  // Server-side event emitters
  emitThinkingStatus(sessionId: string, data: ThinkingStatusEvent['data']): void {
    this.server.to(`session:${sessionId}`).emit('thinking_status', data);
  }

  emitTextChunk(sessionId: string, data: TextChunkEvent['data']): void {
    this.server.to(`session:${sessionId}`).emit('text_chunk', data);
  }

  emitToolCallStart(sessionId: string, data: ToolCallStartEvent['data']): void {
    this.server.to(`session:${sessionId}`).emit('tool_call_start', data);
  }

  emitToolCallResult(sessionId: string, data: ToolCallResultEvent['data']): void {
    this.server.to(`session:${sessionId}`).emit('tool_call_result', data);
  }

  emitToolCallError(sessionId: string, data: ToolCallErrorEvent['data']): void {
    this.server.to(`session:${sessionId}`).emit('tool_call_error', data);
  }

  emitRequestUserInput(sessionId: string, data: RequestUserInputEvent['data']): void {
    this.server.to(`session:${sessionId}`).emit('request_user_input', data);
  }

  emitStateUpdate(sessionId: string, data: StateUpdateEvent['data']): void {
    this.server.to(`session:${sessionId}`).emit('state_update', data);
  }

  emitError(sessionId: string, data: ErrorEvent['data']): void {
    this.server.to(`session:${sessionId}`).emit('error', data);
  }

  private extractTokenFromSocket(client: Socket): string | null {
    const token = 
      client.handshake.auth?.token ||
      client.handshake.headers?.authorization?.replace('Bearer ', '') ||
      client.handshake.query?.token;

    return token as string || null;
  }
}
