// Simple test to verify project structure
const fs = require('fs');
const path = require('path');

console.log('🔍 Testing SynapseAI Project Structure...\n');

// Check if main directories exist
const directories = [
  'apps/backend',
  'apps/frontend', 
  'packages/sdk',
  'packages/shared'
];

directories.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`✅ ${dir} - exists`);
  } else {
    console.log(`❌ ${dir} - missing`);
  }
});

// Check if main files exist
const files = [
  'package.json',
  'tsconfig.json',
  '.env.example',
  'README.md',
  'apps/backend/package.json',
  'apps/backend/src/main.ts',
  'apps/backend/src/app.module.ts',
  'apps/frontend/package.json',
  'apps/frontend/src/app/layout.tsx',
  'apps/frontend/src/app/page.tsx'
];

console.log('\n📁 Checking key files:');
files.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - exists`);
  } else {
    console.log(`❌ ${file} - missing`);
  }
});

console.log('\n🎯 Project structure validation complete!');
console.log('\n📋 Next steps:');
console.log('1. Fix npm installation issues');
console.log('2. Install dependencies');
console.log('3. Test development servers');
console.log('4. Validate authentication flow');
console.log('5. Proceed to APIX WebSocket implementation');
