import { useEffect, useState, useCallback, useRef } from 'react';
import { AIPXClient, AIPXClientConfig, ConnectionStatus } from '../client/apix-client';
import {
  ThinkingStatusEvent,
  TextChunkEvent,
  ToolCallStartEvent,
  ToolCallResultEvent,
  ToolCallErrorEvent,
  RequestUserInputEvent,
  StateUpdateEvent,
  ErrorEvent,
} from '../types/apix-protocol';

export interface UseAIPXOptions extends Partial<AIPXClientConfig> {
  onThinkingStatus?: (data: ThinkingStatusEvent['data']) => void;
  onTextChunk?: (data: TextChunkEvent['data']) => void;
  onToolCallStart?: (data: ToolCallStartEvent['data']) => void;
  onToolCallResult?: (data: ToolCallResultEvent['data']) => void;
  onToolCallError?: (data: ToolCallErrorEvent['data']) => void;
  onRequestUserInput?: (data: RequestUserInputEvent['data']) => void;
  onStateUpdate?: (data: StateUpdateEvent['data']) => void;
  onError?: (data: ErrorEvent['data']) => void;
  onConnected?: () => void;
  onDisconnected?: (reason?: string) => void;
  onConnectionError?: (error: Error) => void;
}

export interface UseAIPXReturn {
  client: AIPXClient | null;
  connectionStatus: ConnectionStatus;
  isConnected: boolean;
  isConnecting: boolean;
  connect: () => Promise<void>;
  disconnect: () => void;
  sendMessage: (message: string, options?: {
    agentId?: string;
    toolId?: string;
    context?: Record<string, any>;
  }) => void;
  sendUserResponse: (requestId: string, response: string, approved?: boolean) => void;
  sendControlSignal: (action: 'stop' | 'pause' | 'resume' | 'restart', reason?: string) => void;
  joinSession: (sessionId: string) => void;
  leaveSession: (sessionId: string) => void;
  ping: () => void;
}

export function useAIPX(options: UseAIPXOptions): UseAIPXReturn {
  const [client, setClient] = useState<AIPXClient | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    connected: false,
    connecting: false,
    reconnectAttempts: 0,
  });
  const optionsRef = useRef(options);

  // Update options ref when options change
  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  // Initialize client
  useEffect(() => {
    if (!options.url || !options.token) {
      return;
    }

    const clientConfig: AIPXClientConfig = {
      url: options.url,
      token: options.token,
      autoConnect: options.autoConnect ?? true,
      reconnection: options.reconnection ?? true,
      reconnectionAttempts: options.reconnectionAttempts ?? 5,
      reconnectionDelay: options.reconnectionDelay ?? 1000,
    };

    const newClient = new AIPXClient(clientConfig);
    setClient(newClient);

    return () => {
      newClient.disconnect();
    };
  }, [options.url, options.token]);

  // Setup event listeners
  useEffect(() => {
    if (!client) return;

    const updateConnectionStatus = () => {
      setConnectionStatus(client.connectionStatus);
    };

    // Connection events
    client.on('connected', () => {
      updateConnectionStatus();
      optionsRef.current.onConnected?.();
    });

    client.on('disconnected', (reason: string) => {
      updateConnectionStatus();
      optionsRef.current.onDisconnected?.(reason);
    });

    client.on('connection_error', (error: Error) => {
      updateConnectionStatus();
      optionsRef.current.onConnectionError?.(error);
    });

    client.on('connecting', updateConnectionStatus);
    client.on('reconnected', updateConnectionStatus);
    client.on('reconnect_attempt', updateConnectionStatus);

    // APIX Protocol events
    client.on('thinking_status', optionsRef.current.onThinkingStatus);
    client.on('text_chunk', optionsRef.current.onTextChunk);
    client.on('tool_call_start', optionsRef.current.onToolCallStart);
    client.on('tool_call_result', optionsRef.current.onToolCallResult);
    client.on('tool_call_error', optionsRef.current.onToolCallError);
    client.on('request_user_input', optionsRef.current.onRequestUserInput);
    client.on('state_update', optionsRef.current.onStateUpdate);
    client.on('error', optionsRef.current.onError);

    // Initial status update
    updateConnectionStatus();

    return () => {
      client.removeAllListeners();
    };
  }, [client]);

  // Memoized methods
  const connect = useCallback(async () => {
    if (!client) throw new Error('Client not initialized');
    return client.connect();
  }, [client]);

  const disconnect = useCallback(() => {
    if (!client) return;
    client.disconnect();
  }, [client]);

  const sendMessage = useCallback((
    message: string,
    options?: {
      agentId?: string;
      toolId?: string;
      context?: Record<string, any>;
    }
  ) => {
    if (!client) throw new Error('Client not initialized');
    client.sendMessage(message, options);
  }, [client]);

  const sendUserResponse = useCallback((
    requestId: string,
    response: string,
    approved?: boolean
  ) => {
    if (!client) throw new Error('Client not initialized');
    client.sendUserResponse(requestId, response, approved);
  }, [client]);

  const sendControlSignal = useCallback((
    action: 'stop' | 'pause' | 'resume' | 'restart',
    reason?: string
  ) => {
    if (!client) throw new Error('Client not initialized');
    client.sendControlSignal(action, reason);
  }, [client]);

  const joinSession = useCallback((sessionId: string) => {
    if (!client) throw new Error('Client not initialized');
    client.joinSession(sessionId);
  }, [client]);

  const leaveSession = useCallback((sessionId: string) => {
    if (!client) throw new Error('Client not initialized');
    client.leaveSession(sessionId);
  }, [client]);

  const ping = useCallback(() => {
    if (!client) throw new Error('Client not initialized');
    client.ping();
  }, [client]);

  return {
    client,
    connectionStatus,
    isConnected: connectionStatus.connected,
    isConnecting: connectionStatus.connecting,
    connect,
    disconnect,
    sendMessage,
    sendUserResponse,
    sendControlSignal,
    joinSession,
    leaveSession,
    ping,
  };
}
