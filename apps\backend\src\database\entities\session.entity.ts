import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Agent } from './agent.entity';
import { Organization } from './organization.entity';
import { Message } from './message.entity';

export enum SessionStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  TERMINATED = 'terminated',
  ERROR = 'error',
}

export enum SessionType {
  CHAT = 'chat',
  TASK = 'task',
  WORKFLOW = 'workflow',
  TESTING = 'testing',
}

@Entity('sessions')
@Index(['userId'])
@Index(['agentId'])
@Index(['organizationId'])
@Index(['status'])
@Index(['createdAt'])
export class Session {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  title: string;

  @Column({
    type: 'enum',
    enum: SessionType,
    default: SessionType.CHAT,
  })
  type: SessionType;

  @Column({
    type: 'enum',
    enum: SessionStatus,
    default: SessionStatus.ACTIVE,
  })
  status: SessionStatus;

  @Column({ type: 'json', nullable: true })
  context: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  memory: {
    shortTerm?: Record<string, any>;
    longTerm?: Record<string, any>;
    variables?: Record<string, any>;
    history?: Array<{
      role: 'user' | 'assistant' | 'system';
      content: string;
      timestamp: Date;
    }>;
  };

  @Column({ type: 'json', nullable: true })
  configuration: {
    maxMessages?: number;
    timeout?: number; // in minutes
    autoSave?: boolean;
    persistMemory?: boolean;
    enableTools?: boolean;
    enableRAG?: boolean;
  };

  @Column({ type: 'json', nullable: true })
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    referrer?: string;
    tags?: string[];
    customData?: Record<string, any>;
  };

  @Column({ type: 'timestamp', nullable: true })
  lastActivityAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Column({ type: 'integer', default: 0 })
  messageCount: number;

  @Column({ type: 'integer', default: 0 })
  tokenCount: number;

  @Column({ type: 'decimal', precision: 10, scale: 6, default: 0 })
  cost: number;

  @Column({ type: 'json', nullable: true })
  analytics: {
    duration?: number; // in seconds
    userSatisfaction?: number; // 1-5 rating
    goalAchieved?: boolean;
    errors?: number;
    toolCalls?: number;
    ragQueries?: number;
  };

  @Column({ type: 'uuid' })
  userId: string;

  @Column({ type: 'uuid' })
  agentId: string;

  @Column({ type: 'uuid' })
  organizationId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Agent, (agent) => agent.sessions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'agentId' })
  agent: Agent;

  @ManyToOne(() => Organization, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'organizationId' })
  organization: Organization;

  @OneToMany(() => Message, (message) => message.session)
  messages: Message[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties
  get isActive(): boolean {
    return this.status === SessionStatus.ACTIVE;
  }

  get isExpired(): boolean {
    return this.expiresAt ? new Date() > this.expiresAt : false;
  }

  get duration(): number {
    if (!this.lastActivityAt) return 0;
    return Math.floor((this.lastActivityAt.getTime() - this.createdAt.getTime()) / 1000);
  }

  get averageResponseTime(): number {
    // Calculate from messages if available
    return this.analytics?.duration || 0;
  }
}
