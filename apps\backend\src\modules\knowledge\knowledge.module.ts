import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { KnowledgeController } from './knowledge.controller';
import { KnowledgeService } from './knowledge.service';
import { KnowledgeBase } from '../../database/entities/knowledge-base.entity';
import { Document } from '../../database/entities/document.entity';

@Module({
  imports: [TypeOrmModule.forFeature([KnowledgeBase, Document])],
  controllers: [KnowledgeController],
  providers: [KnowledgeService],
  exports: [KnowledgeService],
})
export class KnowledgeModule {}
