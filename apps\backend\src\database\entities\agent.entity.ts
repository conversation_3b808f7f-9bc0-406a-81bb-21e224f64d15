import {
  Entity,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Organization } from './organization.entity';
import { Session } from './session.entity';

export enum AgentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
  ARCHIVED = 'archived',
}

export enum AgentType {
  CONVERSATIONAL = 'conversational',
  TASK_ORIENTED = 'task_oriented',
  HYBRID = 'hybrid',
}

@Entity('agents')
@Index(['organizationId'])
@Index(['createdById'])
@Index(['status'])
export class Agent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: AgentType,
    default: AgentType.CONVERSATIONAL,
  })
  type: AgentType;

  @Column({
    type: 'enum',
    enum: AgentStatus,
    default: AgentStatus.DRAFT,
  })
  status: AgentStatus;

  @Column({ type: 'text' })
  systemPrompt: string;

  @Column({ type: 'json', nullable: true })
  promptTemplate: {
    variables?: Record<string, any>;
    examples?: Array<{ input: string; output: string }>;
    instructions?: string[];
  };

  @Column({ type: 'json', nullable: true })
  configuration: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    stopSequences?: string[];
  };

  @Column({ type: 'json', nullable: true })
  capabilities: {
    memoryEnabled?: boolean;
    toolsEnabled?: boolean;
    ragEnabled?: boolean;
    hitlEnabled?: boolean;
    streamingEnabled?: boolean;
  };

  @Column({ type: 'json', nullable: true })
  providers: {
    primary?: string;
    fallback?: string[];
    routing?: 'round_robin' | 'least_latency' | 'cost_optimized';
  };

  @Column({ type: 'json', nullable: true })
  tools: string[]; // Array of tool IDs

  @Column({ type: 'json', nullable: true })
  knowledgeBases: string[]; // Array of knowledge base IDs

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'varchar', length: 255, nullable: true })
  avatar: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  version: string;

  @Column({ type: 'boolean', default: false })
  isPublic: boolean;

  @Column({ type: 'json', nullable: true })
  analytics: {
    totalSessions?: number;
    totalMessages?: number;
    averageRating?: number;
    lastUsed?: Date;
  };

  @Column({ type: 'uuid' })
  organizationId: string;

  @Column({ type: 'uuid' })
  createdById: string;

  @ManyToOne(() => Organization, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'organizationId' })
  organization: Organization;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @OneToMany(() => Session, (session) => session.agent)
  sessions: Session[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties
  get isActive(): boolean {
    return this.status === AgentStatus.ACTIVE;
  }

  get sessionCount(): number {
    return this.sessions?.length || 0;
  }
}
