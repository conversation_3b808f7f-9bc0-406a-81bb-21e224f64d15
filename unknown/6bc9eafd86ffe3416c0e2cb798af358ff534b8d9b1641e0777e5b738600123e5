import 'dotenv/config';
import { DataSource } from 'typeorm';

const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || '5432'),
  username: process.env.DATABASE_USERNAME || 'postgres',
  password: process.env.DATABASE_PASSWORD || '',
  database: process.env.DATABASE_NAME || 'synapseai',
  synchronize: false,
  logging: true,
});

async function updateSchema() {
  try {
    console.log('🔄 Connecting to database...');
    await dataSource.initialize();
    console.log('✅ Database connected successfully');

    console.log('🔄 Updating database schema...');
    
    // Create enums first
    await dataSource.query(`
      DO $$ BEGIN
        CREATE TYPE "organization_status_enum" AS ENUM('active', 'inactive', 'suspended', 'trial');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await dataSource.query(`
      DO $$ BEGIN
        CREATE TYPE "organization_plan_enum" AS ENUM('free', 'starter', 'professional', 'enterprise');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await dataSource.query(`
      DO $$ BEGIN
        CREATE TYPE "user_role_enum" AS ENUM('admin', 'manager', 'user');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await dataSource.query(`
      DO $$ BEGIN
        CREATE TYPE "user_status_enum" AS ENUM('active', 'inactive', 'pending', 'suspended');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Update organizations table
    await dataSource.query(`
      ALTER TABLE "organizations" 
      ADD COLUMN IF NOT EXISTS "logo" character varying(255),
      ADD COLUMN IF NOT EXISTS "status" organization_status_enum DEFAULT 'trial',
      ADD COLUMN IF NOT EXISTS "plan" organization_plan_enum DEFAULT 'free',
      ADD COLUMN IF NOT EXISTS "billing" jsonb DEFAULT '{}',
      ADD COLUMN IF NOT EXISTS "trialEndsAt" timestamp
    `);

    // Update users table
    await dataSource.query(`
      ALTER TABLE "users" 
      ADD COLUMN IF NOT EXISTS "emailVerified" boolean DEFAULT false,
      ADD COLUMN IF NOT EXISTS "emailVerificationToken" character varying,
      ADD COLUMN IF NOT EXISTS "passwordResetToken" character varying,
      ADD COLUMN IF NOT EXISTS "passwordResetExpires" timestamp,
      ADD COLUMN IF NOT EXISTS "avatar" character varying(255),
      ADD COLUMN IF NOT EXISTS "timezone" character varying(50) DEFAULT 'UTC',
      ADD COLUMN IF NOT EXISTS "language" character varying(10) DEFAULT 'en',
      ADD COLUMN IF NOT EXISTS "preferences" jsonb DEFAULT '{}'
    `);

    // Update users table to use enums (handle defaults separately)
    await dataSource.query(`
      ALTER TABLE "users"
      ALTER COLUMN "role" DROP DEFAULT
    `);

    await dataSource.query(`
      ALTER TABLE "users"
      ALTER COLUMN "role" TYPE user_role_enum USING "role"::user_role_enum
    `);

    await dataSource.query(`
      ALTER TABLE "users"
      ALTER COLUMN "role" SET DEFAULT 'user'::user_role_enum
    `);

    await dataSource.query(`
      ALTER TABLE "users"
      ALTER COLUMN "status" DROP DEFAULT
    `);

    await dataSource.query(`
      ALTER TABLE "users"
      ALTER COLUMN "status" TYPE user_status_enum USING "status"::user_status_enum
    `);

    await dataSource.query(`
      ALTER TABLE "users"
      ALTER COLUMN "status" SET DEFAULT 'active'::user_status_enum
    `);

    // Update agents table
    await dataSource.query(`
      ALTER TABLE "agents" 
      ADD COLUMN IF NOT EXISTS "config" jsonb DEFAULT '{}',
      ADD COLUMN IF NOT EXISTS "tools" jsonb DEFAULT '[]',
      ADD COLUMN IF NOT EXISTS "knowledge" jsonb DEFAULT '[]',
      ADD COLUMN IF NOT EXISTS "version" integer DEFAULT 1,
      ADD COLUMN IF NOT EXISTS "lastUsedAt" timestamp
    `);

    // Update sessions table
    await dataSource.query(`
      ALTER TABLE "sessions" 
      ADD COLUMN IF NOT EXISTS "title" character varying(255),
      ADD COLUMN IF NOT EXISTS "summary" text,
      ADD COLUMN IF NOT EXISTS "messageCount" integer DEFAULT 0,
      ADD COLUMN IF NOT EXISTS "lastMessageAt" timestamp
    `);

    // Update messages table
    await dataSource.query(`
      ALTER TABLE "messages" 
      ADD COLUMN IF NOT EXISTS "parentId" uuid,
      ADD COLUMN IF NOT EXISTS "tokens" integer,
      ADD COLUMN IF NOT EXISTS "model" character varying(100),
      ADD COLUMN IF NOT EXISTS "finishReason" character varying(50)
    `);

    // Add foreign key for message parent
    await dataSource.query(`
      ALTER TABLE "messages" 
      DROP CONSTRAINT IF EXISTS "FK_messages_parentId";
      ALTER TABLE "messages" 
      ADD CONSTRAINT "FK_messages_parentId" 
      FOREIGN KEY ("parentId") REFERENCES "messages"("id") ON DELETE SET NULL
    `);

    // Create additional tables that might be missing
    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "tools" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "description" text,
        "type" character varying NOT NULL,
        "config" jsonb DEFAULT '{}',
        "schema" jsonb DEFAULT '{}',
        "isActive" boolean NOT NULL DEFAULT true,
        "organizationId" uuid NOT NULL,
        "createdBy" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_tools" PRIMARY KEY ("id")
      )
    `);

    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "providers" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "type" character varying NOT NULL,
        "config" jsonb DEFAULT '{}',
        "isActive" boolean NOT NULL DEFAULT true,
        "organizationId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_providers" PRIMARY KEY ("id")
      )
    `);

    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "knowledge_bases" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "description" text,
        "type" character varying NOT NULL DEFAULT 'vector',
        "config" jsonb DEFAULT '{}',
        "isActive" boolean NOT NULL DEFAULT true,
        "organizationId" uuid NOT NULL,
        "createdBy" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_knowledge_bases" PRIMARY KEY ("id")
      )
    `);

    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "documents" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "type" character varying NOT NULL,
        "content" text,
        "metadata" jsonb DEFAULT '{}',
        "knowledgeBaseId" uuid NOT NULL,
        "organizationId" uuid NOT NULL,
        "createdBy" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_documents" PRIMARY KEY ("id")
      )
    `);

    await dataSource.query(`
      CREATE TABLE IF NOT EXISTS "analytics_events" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "event" character varying NOT NULL,
        "properties" jsonb DEFAULT '{}',
        "userId" uuid,
        "sessionId" uuid,
        "organizationId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_analytics_events" PRIMARY KEY ("id")
      )
    `);

    // Add missing foreign key constraints
    await dataSource.query(`
      ALTER TABLE "tools" 
      DROP CONSTRAINT IF EXISTS "FK_tools_organizationId";
      ALTER TABLE "tools" 
      ADD CONSTRAINT "FK_tools_organizationId" 
      FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE
    `);

    await dataSource.query(`
      ALTER TABLE "providers" 
      DROP CONSTRAINT IF EXISTS "FK_providers_organizationId";
      ALTER TABLE "providers" 
      ADD CONSTRAINT "FK_providers_organizationId" 
      FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE
    `);

    await dataSource.query(`
      ALTER TABLE "knowledge_bases" 
      DROP CONSTRAINT IF EXISTS "FK_knowledge_bases_organizationId";
      ALTER TABLE "knowledge_bases" 
      ADD CONSTRAINT "FK_knowledge_bases_organizationId" 
      FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE
    `);

    await dataSource.query(`
      ALTER TABLE "documents" 
      DROP CONSTRAINT IF EXISTS "FK_documents_knowledgeBaseId";
      ALTER TABLE "documents" 
      ADD CONSTRAINT "FK_documents_knowledgeBaseId" 
      FOREIGN KEY ("knowledgeBaseId") REFERENCES "knowledge_bases"("id") ON DELETE CASCADE
    `);

    // Create additional indexes
    await dataSource.query(`CREATE INDEX IF NOT EXISTS "IDX_tools_organizationId" ON "tools" ("organizationId")`);
    await dataSource.query(`CREATE INDEX IF NOT EXISTS "IDX_providers_organizationId" ON "providers" ("organizationId")`);
    await dataSource.query(`CREATE INDEX IF NOT EXISTS "IDX_knowledge_bases_organizationId" ON "knowledge_bases" ("organizationId")`);
    await dataSource.query(`CREATE INDEX IF NOT EXISTS "IDX_documents_knowledgeBaseId" ON "documents" ("knowledgeBaseId")`);
    await dataSource.query(`CREATE INDEX IF NOT EXISTS "IDX_analytics_events_organizationId" ON "analytics_events" ("organizationId")`);

    console.log('✅ Database schema updated successfully');

  } catch (error) {
    console.error('❌ Database schema update failed:', error);
    process.exit(1);
  } finally {
    await dataSource.destroy();
  }
}

updateSchema();
