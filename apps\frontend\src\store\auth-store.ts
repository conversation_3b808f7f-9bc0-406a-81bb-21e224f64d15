import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { api } from '@/lib/api';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  organizationId: string;
  avatar?: string;
  preferences?: Record<string, any>;
}

interface Organization {
  id: string;
  name: string;
  slug: string;
  plan: string;
  status: string;
}

interface AuthState {
  user: User | null;
  organization: Organization | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface AuthActions {
  login: (email: string, password: string) => Promise<void>;
  register: (data: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    organizationName?: string;
  }) => Promise<void>;
  logout: () => void;
  refreshAuth: () => Promise<void>;
  initializeAuth: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      organization: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true });
        try {
          const response = await api.post('/auth/login', { email, password });
          const { user, organization, accessToken, refreshToken } = response.data;

          set({
            user,
            organization,
            token: accessToken,
            refreshToken,
            isAuthenticated: true,
            isLoading: false,
          });

          // Set token for future requests
          api.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      register: async (data) => {
        set({ isLoading: true });
        try {
          const response = await api.post('/auth/register', data);
          const { user, organization, accessToken, refreshToken } = response.data;

          set({
            user,
            organization,
            token: accessToken,
            refreshToken,
            isAuthenticated: true,
            isLoading: false,
          });

          // Set token for future requests
          api.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: () => {
        set({
          user: null,
          organization: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
        });

        // Remove token from future requests
        delete api.defaults.headers.common['Authorization'];

        // Clear localStorage
        localStorage.removeItem('auth-storage');
      },

      refreshAuth: async () => {
        const { refreshToken } = get();
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        try {
          const response = await api.post('/auth/refresh', { refreshToken });
          const { user, organization, accessToken, refreshToken: newRefreshToken } = response.data;

          set({
            user,
            organization,
            token: accessToken,
            refreshToken: newRefreshToken,
            isAuthenticated: true,
          });

          // Set token for future requests
          api.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
        } catch (error) {
          // Refresh failed, logout user
          get().logout();
          throw error;
        }
      },

      initializeAuth: async () => {
        const { token, refreshToken } = get();
        
        if (!token) {
          return;
        }

        // Set token for API requests
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        try {
          // Verify token by fetching user profile
          const response = await api.get('/auth/me');
          const user = response.data;

          set({
            user,
            isAuthenticated: true,
          });
        } catch (error) {
          // Token is invalid, try to refresh
          if (refreshToken) {
            try {
              await get().refreshAuth();
            } catch (refreshError) {
              get().logout();
            }
          } else {
            get().logout();
          }
        }
      },

      updateUser: (userData: Partial<User>) => {
        const { user } = get();
        if (user) {
          set({
            user: { ...user, ...userData },
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        organization: state.organization,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
