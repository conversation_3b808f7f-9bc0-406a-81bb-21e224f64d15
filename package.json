{"name": "synapseai", "version": "1.0.0", "description": "Universal AI orchestration platform with click-configurable agents, tools, and real-time WebSocket protocol", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"pnpm run dev:backend\" \"pnpm run dev:frontend\"", "dev:backend": "pnpm --filter apps/backend dev", "dev:frontend": "pnpm --filter apps/frontend dev", "build": "pnpm run build:backend && pnpm run build:frontend", "build:backend": "pnpm --filter apps/backend build", "build:frontend": "pnpm --filter apps/frontend build", "start": "concurrently \"pnpm run start:backend\" \"pnpm run start:frontend\"", "start:backend": "pnpm --filter apps/backend start:prod", "start:frontend": "pnpm --filter apps/frontend start", "test": "pnpm run test:backend && pnpm run test:frontend", "test:backend": "pnpm --filter apps/backend test", "test:frontend": "pnpm --filter apps/frontend test", "lint": "pnpm run lint:backend && pnpm run lint:frontend", "lint:backend": "pnpm --filter apps/backend lint", "lint:frontend": "pnpm --filter apps/frontend lint", "clean": "rimraf node_modules apps/*/node_modules packages/*/node_modules apps/*/dist packages/*/dist", "typecheck": "pnpm run typecheck:backend && pnpm run typecheck:frontend", "typecheck:backend": "pnpm --filter apps/backend typecheck", "typecheck:frontend": "pnpm --filter apps/frontend typecheck"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "rimraf": "^5.0.5", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["ai", "agents", "tools", "websocket", "<PERSON><PERSON><PERSON>", "nextjs", "typescript", "orchestration", "automation"], "author": "SynapseAI Team", "license": "MIT"}