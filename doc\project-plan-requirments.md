### 🧠 Final Production AI Prompt to Build SynapseAI (No Docker, No Mock<PERSON>)

You are an expert fullstack AI developer. Start building **SynapseAI** — a universal, event-based, click-configurable AI orchestration system using **NestJS**, **Next.js App Router**, **Redis**, and **PostgreSQL** — without Docker or placeholder code.

---

### ✅ Rules

1. **No mocks or placeholders** — only real-world, fully functional, tested code.
2. **Real PostgreSQL + Redis support**
3. **JWT + RBAC + Multi-tenant** authentication
4. **Typed WebSocket protocol (APIX)**
5. **Session-aware AI agent logic**
6. **Modular file structure** that matches enterprise conventions
7. **Typed SDK with real WebSocket event bindings**
8. **All modules must be production-ready** with config, validation, retry/fallbacks, and schema definitions.

---

### ⚙️ Stack

* **Backend:** NestJS (WebSocket, REST, PostgreSQL, Redis)
* **Frontend:** Next.js 14 (App Router), <PERSON><PERSON><PERSON> CSS, Shadcn UI
* **Client State:** Zustand for state management
* **Security:** JWT, HTTPS, RBAC
* **Infra:** PM2 + NGINX + Certbot

---

### 🧩 Core Modules (Each Fully UI-Managable)

1:  Auth System — JWT login/register, role/tenant access control
2:  Agent Builder — Click-to-configure agents with state, memory, logic
3:  Tool Manager — Stateless task APIs with input/output config
4:  Tool Agent Builder — Hybrid agents combining tools and memory
5:  Provider Manager — Configure AI providers and smart routing
6:  Session Manager — Real-time Redis memory & TTL context sync
7:  HITL Manager — Manage request_user_input, override decisions
8:  Knowledge Base (RAG) — Upload files/URLs for searchable context
9:  Widget Generator — Embed any agent/tool as JS, iframe, plugin
10: Analytics Dashboard — Track usage, latency, satisfaction metrics
11: User + Role Admin — Manage orgs, tenants, permissions
12" Live Preview Sandbox — Test agents/tools in real time

---

### 🔄 UAUI + APIX Protocol System Architecture

> This section details the internal architecture of SynapseAI’s UAUI engine, its interaction with the WebSocket-based APIX layer, and how real-time flows operate across agents, tools, and providers.

#### 🧠 UAUI Runtime Engine

* **UAUICore.ts** handles agent reasoning, memory access, provider selection, and tool integration.
* **SmartProviderSelector** dynamically routes to OpenAI, Claude, Gemini, Mistral, or Groq using real API calls and contextual evaluation.
* **EventBus** manages broadcasted events with cross-app metadata, plugin middleware, and async subscriptions.
* **StateManager** manages app-level state propagation and Redis-backed sync.
* **RouterEngine** can emit cross-app commands such as `inject_dom_instruction`, `route_to_dashboard`, etc.

#### ⚙️ `@synapseai/aipx` SDK Interface Layer

A production-grade, portable internal SDK that unifies UAUI logic into reusable modules for any frontend or backend.

```ts
export interface AIPXRequest {
  userId: string;
  sessionId: string;
  message: string;
  appType: 'widget' | 'dashboard' | 'crm';
  metadata?: Record<string, any>;
}

export interface AIPXResponse {
  stream?: boolean;
  chunks?: string[];
  final?: string;
  tool_call?: {
    toolId: string;
    params: Record<string, any>;
  };
  error?: string;
  state_update?: Record<string, any>;
}
```

```ts
class AIPXCore {
  registerApp(appId: string, appType: string): AIPXApp;
  processAIRequest(request: AIPXRequest): Promise<AIPXResponse>;
  syncState(fromAppId: string, toAppId: string, state: Record<string, any>): void;
  onEvent(eventType: string, cb: (payload: any) => void): void;
}
```

#### 🔌 APIX WebSocket Protocol

Supported production-grade events:

* `user_message`
* `thinking_status`
* `text_chunk`
* `tool_call_start`, `tool_call_result`, `tool_call_error`
* `request_user_input`, `user_response`
* `state_update`
* `error`
* `control_signal`

#### 🔁 Data Flow

```mermaid
graph TD
U[User Input (UI)] --> WS[APIX WebSocket]
UI --> WS
WS --> UA[UAUICore Engine]

UA --> TM[ToolModule] --> EXT[External API]
UA --> PS[Provider Selector] --> LLM[LLM API]
UA --> SM[SessionManager (Redis)]

UA --> HITL[HITL Manager]
HITL --> HR[Human Responder]
HR --> HITL --> UA

UA --> EV[Emit Events]
EV --> UI[Client SDK]
```

---

### 🧪 Remaining Implementation Priorities

#### 🔌 Multi-Provider Support (Production Logic)

* Claude, Groq, Gemini, Mistral adapters using live APIs
* Retry, fallback, timeout handling
* Latency-aware scoring in SmartProviderSelector
* UI toggle per provider per agent/tool


🛠️ 1. **Standalone Tool Module**
Purpose: Stateless, task-specific microservices (e.g., send email, query DB, run shell command)
Key Features:
Stateless execution via REST or WebSocket
Input/output schema (Zod-based)
Tool Builder UI with:
Input parameter config
Output formatting
Test harness
Retry, fallback, timeout support
APIX Events: tool_call_start, tool_call_result, tool_call_error
Redis-backed session binding (optional)
Secure execution per tenant/org
Analytics: usage count, fail rate, avg latency
Fully embeddable via <script> or iframe (e.g., tool-based calculators)

🤖 2. **Standalone Agent Module**
Purpose: Memory-aware, intelligent agents powered by LLMs for dialogue, planning, or logic orchestration
Key Features:
Session memory (Redis)
Prompt templates + context injection
Agent Builder UI with:
Role/persona config
System prompt templating
Memory and variable bindings
APIX Events: text_chunk, thinking_status, request_user_input, state_update
HITL (Human-in-the-Loop) integration
Knowledge base (RAG) integration
Real-time output streaming
Exportable/embeddable as standalone widget
Full REST + WebSocket endpoints

3. **Tool-Agent Hybrid Module:**
Full Feature Set (Production-Ready)
The Tool-Agent Hybrid module combines the memory/context awareness of Agents with the functional, stateless power of Tools, enabling intelligent reasoning systems that can perform real-world tasks and adapt in real time.

🔧 **ToolAgentBuilder (UI Module)**
A no-code UI interface for creating, chaining, and configuring hybrid workflows.
Click-based builder to combine any existing Agent with one or more Tools
Visual flow editor with conditional logic trees
UI panels for:
Selecting base agent
Inserting tool(s) at decision points
Defining fallback flow
Tool parameter mapping via:
Static values
Context variables (from session, Redis memory)
Prior tool output
Schema validation (Zod) during UI design
Real-time validation: highlight broken links, missing schema bindings
JSON schema export for hybrid chain definition

⚙️ **ToolAgentRunner (Execution Engine)**
A backend orchestration engine that runs hybrid flows at runtime.
Reads hybrid agent definition (exported schema)
Executes the agent via LLM (prompt templating + reasoning)
Detects tool invocation intent mid-agent response
Triggers tool execution:
Binds parameters dynamically
Validates input/output with Zod
Handles retries, timeouts, and fallbacks
Injects tool result into next LLM prompt
Executes next step in the flow, optionally chaining more tools
Emits APIX events:
tool_call_start, tool_call_result, tool_call_error
thinking_status, text_chunk, state_update
Stores execution logs for traceability and replay

🔁 3. **APIX WebSocket Event Integration**
Real-time sync of hybrid logic across frontend and backend.
Broadcast state_update after tool call and agent reasoning
Stream LLM output using text_chunk event
Handle request_user_input from agent flow when human feedback is needed
Emit tool-related events to frontend for live display
Track errors via tool_call_error and show in dashboard

🧠 4. **Prompt Injection & Response Merging**
Smart composition of agent + tool reasoning in real-time.
Agent prompt templates include special placeholders:
{{tool_output}}, {{user_profile}}, {{last_action_result}}
On tool execution:
Response is injected into LLM context
Can alter reasoning path (e.g., "If result X, do Y")
Supports fallback logic:
If tool fails, inject {{fallback_message}} and reroute to safe prompt
Automatic merging of tool output + LLM summary in one final response

🧪 5. **Test + Debug Panel (Optional)**
For admin/dev users to validate hybrid flow before activation.
Manual hybrid flow runner with:
Input simulation
Real API response preview
LLM output trace step-by-step
Schema errors, timeout logs, and fallback triggers shown inline
Session replay mode with full Redis context snapshot

🔐 6. **Security, Roles & Analytics**
Each hybrid agent respects RBAC (admin/dev/viewer)
Execution metrics:
Tool success/failure rate per hybrid
LLM token usage + latency per step
Drop-off points + HITL triggers
Multi-tenant isolation: hybrids scoped per organization

📦 7. **Schema + Storage**
Hybrid schema stored in Postgres
Execution context + state in Redis (per session)
Versioning support: rollback to prior hybrid flows
Syncs with @synapseai/sdk for consistent type exports

---
🌐 **Embedding Options (All Modules)**
Any module (Tool, Agent, Hybrid) can be embedded via:
<script> embed
iFrame
CMS plugin
Public/Private Access
Embed Generator Includes:
Responsive layout config (desktop/mobile/tablet)
Theme (light/dark)
Branding (color, logo, typography)
Greeting text
Optional auth + data sync token


#### 🔁 HITL (Human-in-the-Loop) System

* `request_user_input` → HITL queue with UI dashboard
* Admin override panel with response history log
* Notifications for unresolved HITL prompts
* Session state update after HITL response is submitted
* HITL rule settings per agent/tool (optional/manual/auto)

---

#### 🧠 UAUI Engine Completion

* Memory injection pipeline from Redis to agent context
* Prompt templating logic with variable binding
* StateManager sync: multi-tab, multi-client real-time updates
* Tool/agent schema validation via Zod at runtime

---

#### 🔒 Security & Session Infrastructure

* RBAC enforcement at route + UI-level (admin/dev/viewer)
* Tenant isolation on DB + UI queries
* Redis-backed session persistence
* Rate limiting by tenant/user/module/tool

---

#### 🧩 Tools & Agent Features

* Tool schema config (input/output + validation)
* Test harness for each tool (manual + mock request runner)
* Agent workflow editor step builder + reorder support
* Tool-Agent runner with context and response merging

---

#### 🔍 RAG + Knowledge Base

* Source uploader (file, URL, plain text, DB query)
* Document parser + auto-tagger
* FAISS vectorization and search config
* Source testing UI: query preview, document matching, fallback modes

---

#### 📦 SDK, CLI, & Protocol

* `@synapseai/sdk` typed export for APIX events and UAUI types
* CLI: `init`, `run`, `build`, `preview`, `sync`
* WebSocket client auto-connect, keepalive, retry
* SDK auth token handling, error handling, and hook lifecycle

---

#### 🧪 Widget & Embed Infrastructure
* Widget code generator for JS/iFrame/CMS plugin
* Live Preview with config binding (theme, placement, greeting)
* Responsive mode toggles (desktop/tablet/mobile)
* Branding settings with logo, color, typography injection
Tool Widgets
Example: Document Summarizer, URL Extractor, Form Parser
Use: Inline on landing pages, documentation, support forms
Agent Interfaces
Example: Step-by-step HR Assistant, Lead Generation Wizard
Use: Full-screen takeover or embedded onboarding journey
Tool Agents
Example: Smart Support Bot that uses a document tool
Use: Sidebar, chatbot, or as a pop-in module
Multi-modal Assistants
Voice assistants, image generators, etc
Use: EdTech, accessibility overlays, language tutors
🧩 How It Works
You generate the config via the UI (Tool, Agent, Tool-Agent)
Then you export it as
<script src="https://yourdomain.com/embed/widget.js" data-widget-id="xyz"></script>
Optionally, use WordPress/Shopify plugins or iframes
Config determines functionality, not limited to chat UX
✅ Outcome
Embedding in SynapseAI is modular, AI-type agnostic, and flexible. Whether it’s a one-shot API tool, a live agent, or an assistant flow — it can all be embedded on third-party pages.

---

#### 📊 Analytics & Telemetry

* Per-tool/agent usage tracking (invokes, time, token cost)
* Engagement tracking: bounce, chat depth, drop-off points
* Dashboard UI: graphs, filters, time range, export CSV
* Predictive insights: most used tools, inactive agents, avg latency

---

#### 🌐 Production DevOps

* PM2 process manager for backend
* Certbot TLS for NGINX
* `.env` loader and environment safety checks
* CI/CD pipeline hooks

---

### 🔨 Task List

1. Bootstrap full mono-repo (no Docker)
2. Implement backend auth (NestJS)
3. Setup WebSocket gateway (`/apix`)
4. Create `agent`, `tool`, and `provider` modules with real logic and JSON schemas
5. Use Redis to maintain session state, TTL, and broadcast live updates
6. Build all events for `tool_call_start/result/error`, `thinking_status`, `text_chunk`, `state_update`, `request_user_input`
7. Create API + WebSocket SDK under `/packages/sdk`
8. Create full-featured frontend:

   * Agent Builder (Click to create logic)
   * Provider Manager (Add/Edit Keys)
   * Tool Configurator (Create + Test APIs)
   * Widget Generator (Export with config)
   * Live Chat Preview Sandbox
9. Integrate state management (Zustand) and sync with WebSocket session
10. Production hosting setup:

    * `api.domain.com` → NestJS
    * `app.domain.com` → Next.js
    * TLS: Certbot + NGINX
    * Use PM2 to serve backend
11. Implement full test coverage, `.env`, and docs

---

### ✅ Final Output Expectation

* 100% functional app — no partial/mocked code
* Ready for deployment and integration
* AI-readable modular architecture
* Schema-complete APIX + UAUI protocol
* Embeddable SDK + UI

---

🟢 **Begin building SynapseAI now. All modules must be real, UI-controlled, and deployed-ready.**