# 🧠 SynapseAI

Universal AI orchestration platform with click-configurable agents, tools, and real-time WebSocket protocol.

## 🚀 Features

- **Universal AI Orchestration**: Support for multiple AI providers (OpenAI, Claude, Gemini, Groq, OpenRouter)
- **Click-to-Configure Interface**: No-code agent and tool builder
- **Real-time WebSocket Protocol (APIX)**: Live AI interactions with streaming responses
- **Multi-tenant Architecture**: Organization-based access control
- **Human-in-the-Loop (HITL)**: Human oversight and intervention capabilities
- **Knowledge Base & RAG**: Document upload and context-aware responses
- **Widget System**: Embeddable AI components for any website
- **Production-Ready**: Enterprise-grade security, monitoring, and deployment

## 🏗️ Architecture

### Monorepo Structure
```
synapseai/
├── apps/
│   ├── backend/          # NestJS API server
│   └── frontend/         # Next.js 14 web application
├── packages/
│   ├── sdk/              # TypeScript SDK for APIX protocol
│   └── shared/           # Shared types and utilities
└── docs/                 # Documentation
```

### Tech Stack

**Backend:**
- NestJS with TypeScript
- PostgreSQL database
- Redis for session management
- WebSocket gateway for real-time communication
- JWT authentication with RBAC

**Frontend:**
- Next.js 14 with App Router
- Tailwind CSS + Shadcn UI
- Zustand for state management
- React Query for data fetching
- Dark/light mode support

**Infrastructure:**
- PM2 process management
- NGINX reverse proxy
- SSL/TLS with Certbot
- Docker support (optional)

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis 6+

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd synapseai
npm install
```

2. **Environment setup:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Database setup:**
```bash
# Create PostgreSQL database
createdb synapseai

# Run migrations
npm run db:migrate

# Seed initial data
npm run db:seed
```

4. **Start development servers:**
```bash
# Start both backend and frontend
npm run dev

# Or start individually
npm run dev:backend  # http://localhost:3001
npm run dev:frontend # http://localhost:3000
```

## 📚 API Documentation

- **API Docs**: http://localhost:3001/api/docs (Swagger)
- **WebSocket**: ws://localhost:3001/apix

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start all services
npm run dev:backend      # Backend only
npm run dev:frontend     # Frontend only

# Building
npm run build            # Build all packages
npm run build:backend    # Backend only
npm run build:frontend   # Frontend only

# Testing
npm run test             # Run all tests
npm run test:backend     # Backend tests
npm run test:frontend    # Frontend tests

# Database
npm run db:migrate       # Run migrations
npm run db:seed          # Seed data
npm run db:reset         # Reset database

# Linting
npm run lint             # Lint all packages
npm run lint:fix         # Fix linting issues
```

### Project Structure

```
apps/backend/src/
├── modules/             # Feature modules
│   ├── auth/           # Authentication
│   ├── agents/         # AI agents
│   ├── tools/          # Tool management
│   ├── providers/      # AI providers
│   ├── sessions/       # Session management
│   ├── knowledge/      # Knowledge base
│   └── websocket/      # WebSocket gateway
├── config/             # Configuration
├── database/           # Migrations & seeds
└── common/             # Shared utilities

apps/frontend/src/
├── app/                # Next.js app router
├── components/         # React components
├── hooks/              # Custom hooks
├── lib/                # Utilities
├── store/              # Zustand stores
└── types/              # TypeScript types
```

## 🔌 APIX WebSocket Protocol

SynapseAI uses a custom WebSocket protocol called APIX for real-time AI interactions:

### Event Types
- `user_message` - User input
- `thinking_status` - AI processing status
- `text_chunk` - Streaming text response
- `tool_call_start` - Tool execution begins
- `tool_call_result` - Tool execution result
- `tool_call_error` - Tool execution error
- `request_user_input` - HITL intervention request
- `state_update` - Session state change
- `error` - Error occurred

### Usage Example
```typescript
import { AIPXClient } from '@synapseai/sdk';

const client = new AIPXClient({
  url: 'ws://localhost:3001/apix',
  token: 'your-jwt-token'
});

client.on('text_chunk', (chunk) => {
  console.log('AI response:', chunk);
});

client.sendMessage('Hello, AI!');
```

## 🎯 Core Modules

### 1. Agent Builder
- Visual agent configuration
- Prompt templating
- Memory management
- Provider selection

### 2. Tool Manager
- Stateless tool execution
- Input/output schema validation
- Test harness
- API integrations

### 3. Provider Manager
- Multi-provider support
- Smart routing
- Fallback handling
- Usage analytics

### 4. Knowledge Base
- Document upload
- Vector search
- RAG integration
- Context injection

### 5. Widget System
- Embeddable components
- Theme customization
- Responsive design
- CMS integration

## 🔒 Security

- JWT-based authentication
- Role-based access control (RBAC)
- Multi-tenant isolation
- Rate limiting
- Input validation
- CORS protection

## 📊 Monitoring & Analytics

- Real-time usage metrics
- Performance monitoring
- Error tracking
- User engagement analytics
- Cost tracking per provider

## 🚀 Deployment

### Production Setup

1. **Environment variables:**
```bash
NODE_ENV=production
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
JWT_SECRET=your-secret
```

2. **Build and start:**
```bash
npm run build
npm run start
```

3. **Process management:**
```bash
pm2 start ecosystem.config.js
```

4. **NGINX configuration:**
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    location / {
        proxy_pass http://localhost:3000;
    }
    
    location /api {
        proxy_pass http://localhost:3001;
    }
    
    location /apix {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 📝 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

- Documentation: [docs.synapseai.com](https://docs.synapseai.com)
- Issues: [GitHub Issues](https://github.com/synapseai/synapseai/issues)
- Discord: [Community Server](https://discord.gg/synapseai)

---

Built with ❤️ by the SynapseAI Team
