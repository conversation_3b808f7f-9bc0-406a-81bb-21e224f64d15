import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
  NotFoundException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

import { User, UserStatus } from '../../database/entities/user.entity';
import { Organization, OrganizationStatus } from '../../database/entities/organization.entity';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';

export interface JwtPayload {
  sub: string;
  email: string;
  role: string;
  organizationId: string;
  iat?: number;
  exp?: number;
}

export interface AuthResponse {
  user: Partial<User>;
  organization: Partial<Organization>;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Organization)
    private readonly organizationRepository: Repository<Organization>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  async register(registerDto: RegisterDto): Promise<AuthResponse> {
    this.logger.log(`Registration attempt for email: ${registerDto.email}`);

    try {
      const { email, password, firstName, lastName, organizationName } = registerDto;

      // Validate input data
      if (!email || !password || !firstName || !lastName) {
        this.logger.warn(`Registration failed: Missing required fields for ${email}`);
        throw new BadRequestException('Missing required fields: email, password, firstName, and lastName are required');
      }

      // Check if user already exists
      this.logger.debug(`Checking if user exists: ${email}`);
      const existingUser = await this.userRepository.findOne({
        where: { email },
      });

      if (existingUser) {
        this.logger.warn(`Registration failed: User already exists with email ${email}`);
        throw new ConflictException('User with this email already exists');
      }

      // Create organization if provided
      let organization: Organization;
      if (organizationName) {
        this.logger.debug(`Creating organization: ${organizationName}`);
        const slug = this.generateSlug(organizationName);

        // Check if organization slug already exists
        const existingOrg = await this.organizationRepository.findOne({
          where: { slug },
        });

        if (existingOrg) {
          this.logger.warn(`Registration failed: Organization already exists with slug ${slug}`);
          throw new ConflictException('Organization with this name already exists');
        }

        try {
          organization = this.organizationRepository.create({
            name: organizationName,
            slug,
            status: OrganizationStatus.TRIAL,
            trialEndsAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days trial
          });

          organization = await this.organizationRepository.save(organization);
          this.logger.debug(`Organization created successfully: ${organization.id}`);
        } catch (error) {
          this.logger.error(`Failed to create organization: ${error.message}`, error.stack);
          throw new InternalServerErrorException('Failed to create organization');
        }
      } else {
        this.logger.debug('Using default organization');
        // Find default organization or create one
        try {
          organization = await this.organizationRepository.findOne({
            where: { slug: 'default' },
          });

          if (!organization) {
            this.logger.debug('Creating default organization');
            organization = this.organizationRepository.create({
              name: 'Default Organization',
              slug: 'default',
              status: OrganizationStatus.ACTIVE,
            });
            organization = await this.organizationRepository.save(organization);
            this.logger.debug(`Default organization created: ${organization.id}`);
          }
        } catch (error) {
          this.logger.error(`Failed to handle default organization: ${error.message}`, error.stack);
          throw new InternalServerErrorException('Failed to setup organization');
        }
      }

      // Hash password
      this.logger.debug('Hashing password');
      let hashedPassword: string;
      try {
        hashedPassword = await bcrypt.hash(password, 12);
      } catch (error) {
        this.logger.error(`Failed to hash password: ${error.message}`, error.stack);
        throw new InternalServerErrorException('Failed to process password');
      }

      // Create user
      this.logger.debug(`Creating user: ${email}`);
      let savedUser: User;
      try {
        const user = this.userRepository.create({
          email,
          password: hashedPassword,
          firstName,
          lastName,
          organizationId: organization.id,
          status: UserStatus.ACTIVE,
          emailVerificationToken: uuidv4(),
        });

        savedUser = await this.userRepository.save(user);
        this.logger.log(`User created successfully: ${savedUser.id}`);
      } catch (error) {
        this.logger.error(`Failed to create user: ${error.message}`, error.stack);
        throw new InternalServerErrorException('Failed to create user account');
      }

      // Generate tokens
      this.logger.debug('Generating authentication tokens');
      let tokens: any;
      try {
        tokens = await this.generateTokens(savedUser);
      } catch (error) {
        this.logger.error(`Failed to generate tokens: ${error.message}`, error.stack);
        throw new InternalServerErrorException('Failed to generate authentication tokens');
      }

      this.logger.log(`Registration completed successfully for user: ${savedUser.id}`);
      return {
        user: this.sanitizeUser(savedUser),
        organization: this.sanitizeOrganization(organization),
        ...tokens,
      };

    } catch (error) {
      // Log the full error for debugging
      this.logger.error(`Registration failed for ${registerDto.email}: ${error.message}`, error.stack);

      // Re-throw known exceptions
      if (error instanceof ConflictException ||
          error instanceof BadRequestException ||
          error instanceof InternalServerErrorException) {
        throw error;
      }

      // Handle unexpected errors
      this.logger.error(`Unexpected error during registration: ${error.message}`, error.stack);
      throw new InternalServerErrorException('An unexpected error occurred during registration');
    }
  }

  async login(loginDto: LoginDto): Promise<AuthResponse> {
    this.logger.log(`Login attempt for email: ${loginDto.email}`);

    try {
      const { email, password } = loginDto;

      // Validate input
      if (!email || !password) {
        this.logger.warn(`Login failed: Missing credentials for ${email}`);
        throw new BadRequestException('Email and password are required');
      }

      // Find user with organization
      this.logger.debug(`Looking up user: ${email}`);
      const user = await this.userRepository.findOne({
        where: { email },
        relations: ['organization'],
      });

      if (!user) {
        this.logger.warn(`Login failed: User not found for ${email}`);
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check user status
      if (user.status !== UserStatus.ACTIVE) {
        this.logger.warn(`Login failed: Inactive account for ${email}`);
        throw new UnauthorizedException('Account is not active');
      }

      // Verify password
      this.logger.debug('Verifying password');
      let isPasswordValid: boolean;
      try {
        isPasswordValid = await bcrypt.compare(password, user.password);
      } catch (error) {
        this.logger.error(`Password verification failed: ${error.message}`, error.stack);
        throw new InternalServerErrorException('Authentication error');
      }

      if (!isPasswordValid) {
        this.logger.warn(`Login failed: Invalid password for ${email}`);
        throw new UnauthorizedException('Invalid credentials');
      }

      // Update last login
      try {
        user.lastLoginAt = new Date();
        await this.userRepository.save(user);
        this.logger.debug(`Updated last login for user: ${user.id}`);
      } catch (error) {
        this.logger.warn(`Failed to update last login: ${error.message}`);
        // Don't fail login for this
      }

      // Generate tokens
      this.logger.debug('Generating authentication tokens');
      let tokens: any;
      try {
        tokens = await this.generateTokens(user);
      } catch (error) {
        this.logger.error(`Failed to generate tokens: ${error.message}`, error.stack);
        throw new InternalServerErrorException('Failed to generate authentication tokens');
      }

      this.logger.log(`Login successful for user: ${user.id}`);
      return {
        user: this.sanitizeUser(user),
        organization: this.sanitizeOrganization(user.organization),
        ...tokens,
      };

    } catch (error) {
      // Log the full error for debugging
      this.logger.error(`Login failed for ${loginDto.email}: ${error.message}`, error.stack);

      // Re-throw known exceptions
      if (error instanceof UnauthorizedException ||
          error instanceof BadRequestException ||
          error instanceof InternalServerErrorException) {
        throw error;
      }

      // Handle unexpected errors
      this.logger.error(`Unexpected error during login: ${error.message}`, error.stack);
      throw new InternalServerErrorException('An unexpected error occurred during login');
    }
  }

  async validateUser(email: string, password: string): Promise<User | null> {
    const user = await this.userRepository.findOne({
      where: { email },
      relations: ['organization'],
    });

    if (user && (await bcrypt.compare(password, user.password))) {
      return user;
    }

    return null;
  }

  async validateJwtPayload(payload: JwtPayload): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: payload.sub },
      relations: ['organization'],
    });

    if (!user || user.status !== UserStatus.ACTIVE) {
      throw new UnauthorizedException('User not found or inactive');
    }

    return user;
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    const { currentPassword, newPassword } = changePasswordDto;

    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    const hashedNewPassword = await bcrypt.hash(newPassword, 12);
    user.password = hashedNewPassword;

    await this.userRepository.save(user);
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void> {
    const { email } = forgotPasswordDto;

    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user) {
      // Don't reveal if user exists
      return;
    }

    const resetToken = uuidv4();
    const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    user.passwordResetToken = resetToken;
    user.passwordResetExpiresAt = resetExpires;

    await this.userRepository.save(user);

    // TODO: Send email with reset token
    // await this.emailService.sendPasswordResetEmail(user.email, resetToken);
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    const { token, newPassword } = resetPasswordDto;

    const user = await this.userRepository.findOne({
      where: {
        passwordResetToken: token,
      },
    });

    if (!user || !user.passwordResetExpiresAt || user.passwordResetExpiresAt < new Date()) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    const hashedPassword = await bcrypt.hash(newPassword, 12);
    user.password = hashedPassword;
    user.passwordResetToken = null;
    user.passwordResetExpiresAt = null;

    await this.userRepository.save(user);
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
      });

      const user = await this.userRepository.findOne({
        where: { id: payload.sub },
        relations: ['organization'],
      });

      if (!user || user.status !== UserStatus.ACTIVE) {
        throw new UnauthorizedException('User not found or inactive');
      }

      const tokens = await this.generateTokens(user);

      return {
        user: this.sanitizeUser(user),
        organization: this.sanitizeOrganization(user.organization),
        ...tokens,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  private async generateTokens(user: User): Promise<{
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  }> {
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      organizationId: user.organizationId,
    };

    const accessToken = this.jwtService.sign(payload);
    
    const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get('JWT_REFRESH_SECRET'),
      expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN', '30d'),
    });

    const expiresIn = parseInt(this.configService.get('JWT_EXPIRES_IN', '604800')); // 7 days in seconds

    return {
      accessToken,
      refreshToken,
      expiresIn,
    };
  }

  private sanitizeUser(user: User): Partial<User> {
    const { password, passwordResetToken, emailVerificationToken, ...sanitized } = user;
    return sanitized;
  }

  private sanitizeOrganization(organization: Organization): Partial<Organization> {
    const { billing, ...sanitized } = organization;
    return sanitized;
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
}
