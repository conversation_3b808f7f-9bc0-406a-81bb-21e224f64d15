export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => string | null;
}

export class FormValidator {
  // Email validation
  static validateEmail(email: string): ValidationResult {
    if (!email) {
      return { isValid: false, error: 'Email is required' };
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { isValid: false, error: 'Please enter a valid email address' };
    }
    
    return { isValid: true };
  }

  // Password validation with strength requirements
  static validatePassword(password: string, options?: {
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSpecialChars?: boolean;
    minLength?: number;
  }): ValidationResult {
    const opts = {
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: false,
      minLength: 8,
      ...options,
    };

    if (!password) {
      return { isValid: false, error: 'Password is required' };
    }

    if (password.length < opts.minLength) {
      return { isValid: false, error: `Password must be at least ${opts.minLength} characters long` };
    }

    if (opts.requireLowercase && !/(?=.*[a-z])/.test(password)) {
      return { isValid: false, error: 'Password must contain at least one lowercase letter' };
    }

    if (opts.requireUppercase && !/(?=.*[A-Z])/.test(password)) {
      return { isValid: false, error: 'Password must contain at least one uppercase letter' };
    }

    if (opts.requireNumbers && !/(?=.*\d)/.test(password)) {
      return { isValid: false, error: 'Password must contain at least one number' };
    }

    if (opts.requireSpecialChars && !/(?=.*[!@#$%^&*(),.?":{}|<>])/.test(password)) {
      return { isValid: false, error: 'Password must contain at least one special character' };
    }

    return { isValid: true };
  }

  // Name validation
  static validateName(name: string, fieldName: string = 'Name'): ValidationResult {
    if (!name) {
      return { isValid: false, error: `${fieldName} is required` };
    }

    if (name.length < 2) {
      return { isValid: false, error: `${fieldName} must be at least 2 characters long` };
    }

    if (name.length > 50) {
      return { isValid: false, error: `${fieldName} must be less than 50 characters long` };
    }

    if (!/^[a-zA-Z\s'-]+$/.test(name)) {
      return { isValid: false, error: `${fieldName} can only contain letters, spaces, hyphens, and apostrophes` };
    }

    return { isValid: true };
  }

  // Organization name validation
  static validateOrganizationName(name: string): ValidationResult {
    if (!name) {
      return { isValid: false, error: 'Organization name is required' };
    }

    if (name.length < 2) {
      return { isValid: false, error: 'Organization name must be at least 2 characters long' };
    }

    if (name.length > 100) {
      return { isValid: false, error: 'Organization name must be less than 100 characters long' };
    }

    if (!/^[a-zA-Z0-9\s\-_.&]+$/.test(name)) {
      return { isValid: false, error: 'Organization name contains invalid characters' };
    }

    return { isValid: true };
  }

  // Phone number validation
  static validatePhoneNumber(phone: string): ValidationResult {
    if (!phone) {
      return { isValid: false, error: 'Phone number is required' };
    }

    // Remove all non-digit characters for validation
    const digitsOnly = phone.replace(/\D/g, '');
    
    if (digitsOnly.length < 10) {
      return { isValid: false, error: 'Phone number must be at least 10 digits' };
    }

    if (digitsOnly.length > 15) {
      return { isValid: false, error: 'Phone number must be less than 15 digits' };
    }

    return { isValid: true };
  }

  // URL validation
  static validateUrl(url: string): ValidationResult {
    if (!url) {
      return { isValid: false, error: 'URL is required' };
    }

    try {
      new URL(url);
      return { isValid: true };
    } catch {
      return { isValid: false, error: 'Please enter a valid URL' };
    }
  }

  // Generic field validation
  static validateField(value: string, rules: ValidationRule, fieldName: string = 'Field'): ValidationResult {
    if (rules.required && !value) {
      return { isValid: false, error: `${fieldName} is required` };
    }

    if (value && rules.minLength && value.length < rules.minLength) {
      return { isValid: false, error: `${fieldName} must be at least ${rules.minLength} characters long` };
    }

    if (value && rules.maxLength && value.length > rules.maxLength) {
      return { isValid: false, error: `${fieldName} must be less than ${rules.maxLength} characters long` };
    }

    if (value && rules.pattern && !rules.pattern.test(value)) {
      return { isValid: false, error: `${fieldName} format is invalid` };
    }

    if (value && rules.custom) {
      const customError = rules.custom(value);
      if (customError) {
        return { isValid: false, error: customError };
      }
    }

    return { isValid: true };
  }

  // Password confirmation validation
  static validatePasswordConfirmation(password: string, confirmPassword: string): ValidationResult {
    if (!confirmPassword) {
      return { isValid: false, error: 'Please confirm your password' };
    }

    if (password !== confirmPassword) {
      return { isValid: false, error: 'Passwords do not match' };
    }

    return { isValid: true };
  }

  // Batch validation for forms
  static validateForm(fields: Record<string, { value: string; rules: ValidationRule; fieldName?: string }>): {
    isValid: boolean;
    errors: Record<string, string>;
    firstError?: string;
  } {
    const errors: Record<string, string> = {};
    let firstError: string | undefined;

    for (const [fieldKey, { value, rules, fieldName }] of Object.entries(fields)) {
      const result = this.validateField(value, rules, fieldName || fieldKey);
      if (!result.isValid && result.error) {
        errors[fieldKey] = result.error;
        if (!firstError) {
          firstError = result.error;
        }
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      firstError,
    };
  }
}

// Password strength calculator
export function calculatePasswordStrength(password: string): {
  score: number; // 0-100
  level: 'weak' | 'fair' | 'good' | 'strong';
  feedback: string[];
} {
  let score = 0;
  const feedback: string[] = [];

  if (password.length >= 8) {
    score += 25;
  } else {
    feedback.push('Use at least 8 characters');
  }

  if (/[a-z]/.test(password)) {
    score += 15;
  } else {
    feedback.push('Add lowercase letters');
  }

  if (/[A-Z]/.test(password)) {
    score += 15;
  } else {
    feedback.push('Add uppercase letters');
  }

  if (/\d/.test(password)) {
    score += 15;
  } else {
    feedback.push('Add numbers');
  }

  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 15;
  } else {
    feedback.push('Add special characters');
  }

  if (password.length >= 12) {
    score += 10;
  }

  if (/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])/.test(password)) {
    score += 5;
  }

  let level: 'weak' | 'fair' | 'good' | 'strong';
  if (score < 40) {
    level = 'weak';
  } else if (score < 60) {
    level = 'fair';
  } else if (score < 80) {
    level = 'good';
  } else {
    level = 'strong';
  }

  return { score, level, feedback };
}
