import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AnalyticsEvent } from '../../database/entities/analytics-event.entity';

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(AnalyticsEvent)
    private readonly analyticsEventRepository: Repository<AnalyticsEvent>,
  ) {}

  async getOverview(organizationId: string) {
    const totalEvents = await this.analyticsEventRepository.count({
      where: { organizationId },
    });

    return {
      totalEvents,
      // More analytics will be implemented later
    };
  }
}
