import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import * as bcrypt from 'bcryptjs';

import { User, UserRole, UserStatus } from '../../database/entities/user.entity';
import { Organization } from '../../database/entities/organization.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UpdateUserProfileDto } from './dto/update-user-profile.dto';

export interface PaginatedUsers {
  users: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Organization)
    private readonly organizationRepository: Repository<Organization>,
  ) {}

  async create(createUserDto: CreateUserDto, currentUser: User): Promise<User> {
    const { email, password, organizationId, ...userData } = createUserDto;

    // Check if user already exists
    const existingUser = await this.userRepository.findOne({
      where: { email },
    });

    if (existingUser) {
      throw new BadRequestException('User with this email already exists');
    }

    // Verify organization exists and user has access
    const organization = await this.organizationRepository.findOne({
      where: { id: organizationId || currentUser.organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Check permissions
    if (organizationId && organizationId !== currentUser.organizationId && !currentUser.isAdmin) {
      throw new ForbiddenException('Cannot create user in different organization');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const user = this.userRepository.create({
      ...userData,
      email,
      password: hashedPassword,
      organizationId: organizationId || currentUser.organizationId,
      status: UserStatus.ACTIVE,
    });

    return this.userRepository.save(user);
  }

  async findAll(
    organizationId: string,
    page: number = 1,
    limit: number = 10,
    search?: string,
    role?: UserRole,
    status?: UserStatus,
  ): Promise<PaginatedUsers> {
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.organization', 'organization')
      .where('user.organizationId = :organizationId', { organizationId });

    if (search) {
      queryBuilder.andWhere(
        '(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    if (status) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    const [users, total] = await queryBuilder
      .orderBy('user.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, organizationId: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id, organizationId },
      relations: ['organization'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email },
      relations: ['organization'],
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto, currentUser: User): Promise<User> {
    const user = await this.findOne(id, currentUser.organizationId);

    // Check permissions
    if (user.id !== currentUser.id && !currentUser.isAdmin) {
      throw new ForbiddenException('Cannot update other users');
    }

    // Prevent role escalation
    if (updateUserDto.role && updateUserDto.role !== user.role) {
      if (!currentUser.isAdmin) {
        throw new ForbiddenException('Cannot change user role');
      }

      // Prevent non-super-admin from creating super-admin
      if (updateUserDto.role === UserRole.SUPER_ADMIN && currentUser.role !== UserRole.SUPER_ADMIN) {
        throw new ForbiddenException('Cannot assign super admin role');
      }
    }

    // Hash password if provided
    if (updateUserDto.password) {
      updateUserDto.password = await bcrypt.hash(updateUserDto.password, 12);
    }

    Object.assign(user, updateUserDto);
    return this.userRepository.save(user);
  }

  async updateProfile(id: string, updateProfileDto: UpdateUserProfileDto): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['organization'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    Object.assign(user, updateProfileDto);
    return this.userRepository.save(user);
  }

  async remove(id: string, currentUser: User): Promise<void> {
    const user = await this.findOne(id, currentUser.organizationId);

    // Check permissions
    if (!currentUser.isAdmin) {
      throw new ForbiddenException('Only admins can delete users');
    }

    // Prevent self-deletion
    if (user.id === currentUser.id) {
      throw new BadRequestException('Cannot delete your own account');
    }

    // Prevent deleting super admin
    if (user.role === UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('Cannot delete super admin');
    }

    await this.userRepository.remove(user);
  }

  async updateLastLogin(id: string, ip?: string): Promise<void> {
    await this.userRepository.update(id, {
      lastLoginAt: new Date(),
      lastLoginIp: ip,
    });
  }

  async getOrganizationStats(organizationId: string): Promise<{
    totalUsers: number;
    activeUsers: number;
    usersByRole: Record<UserRole, number>;
    recentUsers: User[];
  }> {
    const totalUsers = await this.userRepository.count({
      where: { organizationId },
    });

    const activeUsers = await this.userRepository.count({
      where: { organizationId, status: UserStatus.ACTIVE },
    });

    const usersByRole = {} as Record<UserRole, number>;
    for (const role of Object.values(UserRole)) {
      usersByRole[role] = await this.userRepository.count({
        where: { organizationId, role },
      });
    }

    const recentUsers = await this.userRepository.find({
      where: { organizationId },
      order: { createdAt: 'DESC' },
      take: 5,
    });

    return {
      totalUsers,
      activeUsers,
      usersByRole,
      recentUsers,
    };
  }
}
