import { ConfigService } from '@nestjs/config';
import { JwtModuleOptions } from '@nestjs/jwt';

export const jwtConfig = (configService: ConfigService): JwtModuleOptions => ({
  secret: configService.get('JWT_SECRET'),
  signOptions: {
    expiresIn: configService.get('JWT_EXPIRES_IN', '7d'),
  },
});

export const jwtRefreshConfig = (configService: ConfigService) => ({
  secret: configService.get('JWT_REFRESH_SECRET'),
  expiresIn: configService.get('JWT_REFRESH_EXPIRES_IN', '30d'),
});
