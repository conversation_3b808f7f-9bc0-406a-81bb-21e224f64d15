import React from 'react';
import Link from 'next/link';
import { Brain, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
  backButtonText?: string;
  backButtonHref?: string;
  className?: string;
}

export function AuthLayout({
  children,
  title,
  subtitle,
  showBackButton = false,
  backButtonText = 'Back to Home',
  backButtonHref = '/',
  className,
}: AuthLayoutProps) {
  return (
    <div className={cn('min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50', className)}>
      <div className="flex min-h-screen">
        {/* Left Column - Branding & Information */}
        <div className="hidden lg:flex lg:w-1/2 xl:w-2/5 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-black/10">
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent" />
            <div className="absolute top-0 left-0 w-full h-full">
              <div className="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full blur-xl" />
              <div className="absolute bottom-40 right-20 w-48 h-48 bg-white/5 rounded-full blur-2xl" />
              <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-white/10 rounded-full blur-lg" />
            </div>
          </div>
          
          <div className="relative z-10 flex flex-col justify-between p-12 text-white">
            {/* Header */}
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                <Brain className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">SynapseAI</h1>
                <p className="text-blue-100 text-sm">Intelligent AI Platform</p>
              </div>
            </div>
            
            {/* Main Content */}
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-4xl font-bold leading-tight">
                  Build Powerful AI Agents with Ease
                </h2>
                <p className="text-xl text-blue-100 leading-relaxed">
                  Create, deploy, and manage intelligent AI agents that understand your business needs and deliver exceptional results.
                </p>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-300 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold">Visual Agent Builder</h3>
                    <p className="text-blue-100 text-sm">Drag-and-drop interface for creating complex AI workflows</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-purple-300 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold">Real-time Collaboration</h3>
                    <p className="text-blue-100 text-sm">Work together with your team in real-time</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-300 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold">Enterprise Security</h3>
                    <p className="text-blue-100 text-sm">Bank-level security with SOC 2 compliance</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Footer */}
            <div className="text-blue-100 text-sm">
              <p>&copy; 2024 SynapseAI. All rights reserved.</p>
            </div>
          </div>
        </div>
        
        {/* Right Column - Form */}
        <div className="flex-1 lg:w-1/2 xl:w-3/5 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-6 lg:p-8">
            {showBackButton && (
              <Button variant="ghost" size="sm">
                <Link href={backButtonHref} className="flex items-center space-x-2">
                  <ArrowLeft className="h-4 w-4" />
                  <span>{backButtonText}</span>
                </Link>
              </Button>
            )}
            
            {/* Mobile Logo */}
            <div className="lg:hidden flex items-center space-x-2">
              <Brain className="h-6 w-6 text-blue-600" />
              <span className="font-bold text-lg">SynapseAI</span>
            </div>
            
            <div className="hidden lg:block" />
          </div>
          
          {/* Form Container */}
          <div className="flex-1 flex items-center justify-center p-6 lg:p-8">
            <div className="w-full max-w-md space-y-8">
              {/* Form Header */}
              <div className="text-center space-y-2">
                <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                  {title}
                </h1>
                {subtitle && (
                  <p className="text-gray-600">
                    {subtitle}
                  </p>
                )}
              </div>
              
              {/* Form Content */}
              <div className="space-y-6">
                {children}
              </div>
            </div>
          </div>
          
          {/* Mobile Features */}
          <div className="lg:hidden p-6 bg-gray-50 border-t">
            <div className="text-center space-y-4">
              <h3 className="font-semibold text-gray-900">Why Choose SynapseAI?</h3>
              <div className="grid grid-cols-1 gap-3 text-sm text-gray-600">
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                  <span>Visual Agent Builder</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-purple-500 rounded-full" />
                  <span>Real-time Collaboration</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                  <span>Enterprise Security</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Loading overlay component for form submissions
export function FormLoadingOverlay({ isVisible }: { isVisible: boolean }) {
  if (!isVisible) return null;
  
  return (
    <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
      <div className="flex flex-col items-center space-y-3">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
        <p className="text-sm text-gray-600">Processing...</p>
      </div>
    </div>
  );
}
