import { PartialType } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { CreateOrganizationDto } from './create-organization.dto';
import { OrganizationStatus, OrganizationPlan } from '../../../database/entities/organization.entity';

export class UpdateOrganizationDto extends PartialType(CreateOrganizationDto) {
  @IsOptional()
  @IsEnum(OrganizationStatus)
  status?: OrganizationStatus;

  @IsOptional()
  @IsEnum(OrganizationPlan)
  plan?: OrganizationPlan;
}
