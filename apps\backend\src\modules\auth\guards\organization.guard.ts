import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ORGANIZATION_RESOURCE_KEY } from '../decorators/organization-resource.decorator';

@Injectable()
export class OrganizationGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const resourceKey = this.reflector.getAllAndOverride<string>(ORGANIZATION_RESOURCE_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!resourceKey) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const { user } = request;
    
    if (!user) {
      return false;
    }

    // Super admin can access all organizations
    if (user.role === 'super_admin') {
      return true;
    }

    // Get organization ID from request params, body, or query
    const organizationId = 
      request.params?.organizationId || 
      request.body?.organizationId || 
      request.query?.organizationId;

    // If no organization ID in request, allow (will be handled by business logic)
    if (!organizationId) {
      return true;
    }

    // Check if user belongs to the organization
    if (user.organizationId !== organizationId) {
      throw new ForbiddenException('Access denied to this organization resource');
    }

    return true;
  }
}
