{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/config/*": ["src/config/*"], "@/modules/*": ["src/modules/*"], "@/common/*": ["src/common/*"], "@/database/*": ["src/database/*"], "@synapseai/sdk": ["../../packages/sdk/src"], "@synapseai/shared": ["../../packages/shared/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test"]}