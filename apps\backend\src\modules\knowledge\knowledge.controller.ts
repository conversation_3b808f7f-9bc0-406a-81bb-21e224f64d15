import { Controller, Get, UseGuards } from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON><PERSON>, Api<PERSON><PERSON>erAuth } from '@nestjs/swagger';

import { KnowledgeService } from './knowledge.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../../database/entities/user.entity';

@ApiTags('knowledge')
@Controller('knowledge')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class KnowledgeController {
  constructor(private readonly knowledgeService: KnowledgeService) {}

  @Get()
  async findAll(@CurrentUser() currentUser: User) {
    return this.knowledgeService.findAll(currentUser.organizationId);
  }
}
